/* Universal CRUD Operations CSS Styles */
/* This file can be used by multiple CRUD scenarios for consistent styling */

/* Base Styles */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

/* Headers */
h1 {
    color: #2c3e50;
    text-align: center;
    margin-bottom: 30px;
    font-size: 2.5em;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

h2 {
    color: #34495e;
    border-bottom: 3px solid #3498db;
    padding-bottom: 10px;
    margin: 30px 0 20px 0;
    font-size: 1.8em;
}

h3 {
    color: #2c3e50;
    margin: 20px 0 15px 0;
    font-size: 1.4em;
}

/* Navigation Buttons */
.nav-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin: 30px 0;
    padding: 25px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    flex-wrap: wrap;
}

.btn {
    display: inline-block;
    padding: 12px 24px;
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    color: white;
    text-decoration: none;
    border-radius: 8px;
    border: none;
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn:hover {
    background: linear-gradient(135deg, #2980b9 0%, #1f618d 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0,0,0,0.2);
}

/* Tables */
table {
    width: 100%;
    border-collapse: collapse;
    margin: 25px 0;
    background-color: white;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    border-radius: 10px;
    overflow: hidden;
}

th, td {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid #ecf0f1;
}

th {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    color: white;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.9em;
    letter-spacing: 1px;
}

tr:nth-child(even) {
    background-color: #f8f9fa;
}

tr:hover {
    background-color: #e3f2fd;
    transform: scale(1.01);
    transition: all 0.2s ease;
}

/* Forms */
.form-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 30px;
    margin: 25px 0;
    border-radius: 12px;
    border-left: 5px solid #3498db;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

form {
    margin: 20px 0;
}

input[type="text"], 
input[type="email"], 
input[type="tel"],
input[type="number"],
input[type="date"],
input[type="password"],
select,
textarea {
    width: 100%;
    padding: 12px 15px;
    margin: 8px 0;
    border: 2px solid #bdc3c7;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
    background-color: white;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="tel"]:focus,
input[type="number"]:focus,
input[type="date"]:focus,
input[type="password"]:focus,
select:focus,
textarea:focus {
    border-color: #3498db;
    outline: none;
    box-shadow: 0 0 10px rgba(52, 152, 219, 0.3);
    transform: translateY(-1px);
}

input[type="submit"],
button {
    background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
    color: white;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    margin: 10px 5px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

input[type="submit"]:hover,
button:hover {
    background: linear-gradient(135deg, #229954 0%, #1e8449 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0,0,0,0.2);
}

/* Special Buttons */
.delete-btn {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%) !important;
    padding: 8px 16px !important;
    font-size: 14px !important;
    margin: 2px !important;
}

.delete-btn:hover {
    background: linear-gradient(135deg, #c0392b 0%, #a93226 100%) !important;
}

.update-btn {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%) !important;
}

.update-btn:hover {
    background: linear-gradient(135deg, #e67e22 0%, #d35400 100%) !important;
}

/* Record Display */
.record {
    background-color: white;
    padding: 20px;
    margin: 15px 0;
    border-radius: 10px;
    border-left: 4px solid #3498db;
    box-shadow: 0 2px 6px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.record:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    transform: translateX(5px);
}

/* Statistics Cards */
.stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin: 30px 0;
}

.stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 25px;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 6px 12px rgba(0,0,0,0.15);
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-card h3 {
    margin: 0 0 10px 0;
    font-size: 2.5em;
    font-weight: bold;
    color: white;
}

.stat-card p {
    margin: 0;
    font-size: 1.2em;
    opacity: 0.9;
}

/* Messages */
.success-message {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
    padding: 15px 20px;
    border: 1px solid #c3e6cb;
    border-radius: 8px;
    margin: 15px 0;
    border-left: 4px solid #28a745;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.error-message {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
    padding: 15px 20px;
    border: 1px solid #f5c6cb;
    border-radius: 8px;
    margin: 15px 0;
    border-left: 4px solid #dc3545;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Links */
a {
    color: #3498db;
    text-decoration: none;
    transition: color 0.3s ease;
}

a:hover {
    color: #2980b9;
    text-decoration: underline;
}

/* Back Link */
.back-link {
    display: inline-block;
    margin-bottom: 20px;
    padding: 8px 16px;
    background-color: #95a5a6;
    color: white;
    border-radius: 5px;
    text-decoration: none;
    transition: background-color 0.3s ease;
}

.back-link:hover {
    background-color: #7f8c8d;
    color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
    body {
        padding: 10px;
    }
    
    .container {
        padding: 20px;
    }
    
    .nav-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .nav-buttons .btn {
        width: 100%;
        max-width: 300px;
        margin: 5px 0;
    }
    
    table {
        font-size: 12px;
    }
    
    th, td {
        padding: 8px;
    }
    
    .stats {
        grid-template-columns: 1fr;
    }
    
    h1 {
        font-size: 2em;
    }
    
    h2 {
        font-size: 1.5em;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 15px;
    }
    
    h1 {
        font-size: 1.8em;
    }
    
    .form-section {
        padding: 20px;
    }
}
