"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON>, AlertCircle, <PERSON><PERSON>, ArrowLeft } from "lucide-react";
import Link from "next/link";

interface SubnetInfo {
  networkAddress: string;
  broadcastAddress: string;
  firstHost: string;
  lastHost: string;
  subnetMask: string;
  totalHosts: number;
  usableHosts: number;
  subnetBits: number;
  hostBits: number;
  binaryNetwork: string;
  binaryMask: string;
}

// Helper functions
const getNetworkClass = (ip: string): string => {
  const firstOctet = parseInt(ip.split(".")[0]);
  if (firstOctet >= 1 && firstOctet <= 126) return "Class A";
  if (firstOctet >= 128 && firstOctet <= 191) return "Class B";
  if (firstOctet >= 192 && firstOctet <= 223) return "Class C";
  if (firstOctet >= 224 && firstOctet <= 239) return "Class D (Multicast)";
  if (firstOctet >= 240 && firstOctet <= 255) return "Class E (Reserved)";
  return "Invalid";
};

const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text);
  } catch (err) {
    console.error("Failed to copy: ", err);
  }
};

export default function SubnetCalculatorPage() {
  const [mounted, setMounted] = useState(false);
  const [ipAddress, setIpAddress] = useState("***********");
  const [cidr, setCidr] = useState(24);
  const [subnetInfo, setSubnetInfo] = useState<SubnetInfo | null>(null);
  const [error, setError] = useState("");
  const [networkClass, setNetworkClass] = useState("");

  // Ensure component is mounted before rendering dynamic content
  useEffect(() => {
    setMounted(true);
  }, []);

  // Calculate subnet information
  const calculateSubnet = (ip: string, cidrValue: number) => {
    try {
      setError("");

      // Validate IP address
      const ipParts = ip.split(".").map(Number);
      if (
        ipParts.length !== 4 ||
        ipParts.some((part) => part < 0 || part > 255)
      ) {
        throw new Error("Invalid IP address format");
      }

      // Validate CIDR
      if (cidrValue < 0 || cidrValue > 32) {
        throw new Error("CIDR must be between 0 and 32");
      }

      // Set network class
      setNetworkClass(getNetworkClass(ip));

      // Convert IP to 32-bit integer
      const ipInt =
        (ipParts[0] << 24) +
        (ipParts[1] << 16) +
        (ipParts[2] << 8) +
        ipParts[3];

      // Calculate subnet mask
      const maskInt = (0xffffffff << (32 - cidrValue)) >>> 0;

      // Calculate network address
      const networkInt = (ipInt & maskInt) >>> 0;

      // Calculate broadcast address
      const broadcastInt = (networkInt | (0xffffffff >>> cidrValue)) >>> 0;

      // Convert back to dotted decimal
      const intToIp = (int: number) =>
        [
          (int >>> 24) & 0xff,
          (int >>> 16) & 0xff,
          (int >>> 8) & 0xff,
          int & 0xff,
        ].join(".");

      const networkAddress = intToIp(networkInt);
      const broadcastAddress = intToIp(broadcastInt);
      const subnetMask = intToIp(maskInt);

      // Calculate first and last host
      const firstHost = intToIp(networkInt + 1);
      const lastHost = intToIp(broadcastInt - 1);

      // Calculate host counts
      const hostBits = 32 - cidrValue;
      const totalHosts = Math.pow(2, hostBits);
      const usableHosts = totalHosts - 2; // Subtract network and broadcast

      // Binary representations
      const toBinary = (int: number) => {
        return (
          int.toString(2).padStart(32, "0").match(/.{8}/g)?.join(".") || ""
        );
      };

      const binaryNetwork = toBinary(networkInt);
      const binaryMask = toBinary(maskInt);

      setSubnetInfo({
        networkAddress,
        broadcastAddress,
        firstHost,
        lastHost,
        subnetMask,
        totalHosts,
        usableHosts,
        subnetBits: cidrValue,
        hostBits,
        binaryNetwork,
        binaryMask,
      });
    } catch (err) {
      setError(err instanceof Error ? err.message : "Calculation error");
      setSubnetInfo(null);
    }
  };

  // Auto-calculate when inputs change
  useEffect(() => {
    if (mounted) {
      calculateSubnet(ipAddress, cidr);
    }
  }, [ipAddress, cidr, mounted]);

  // Don't render until mounted to prevent hydration mismatch
  if (!mounted) {
    return (
      <div className="space-y-8">
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white">
            Subnet Calculator
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Calculate subnet information with detailed binary representations.
          </p>
        </div>
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
          <div className="animate-pulse">
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-4"></div>
            <div className="space-y-3">
              <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded"></div>
              <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <Link
          href="/calculator"
          className="inline-flex items-center text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 mb-4"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Calculator Hub
        </Link>
        <h1 className="text-4xl font-bold text-gray-900 dark:text-white">
          Subnet Calculator
        </h1>
        <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
          Calculate subnet information, network addresses, and host ranges with
          detailed binary representations.
        </p>
      </div>

      {/* Calculator Input */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white flex items-center">
          <Calculator className="h-5 w-5 mr-2 text-blue-600 dark:text-blue-400" />
          Input Parameters
        </h2>

        <div className="grid md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              IP Address
            </label>
            <input
              type="text"
              value={ipAddress}
              onChange={(e) => setIpAddress(e.target.value)}
              placeholder="***********"
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              suppressHydrationWarning
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              CIDR Notation (/{cidr})
            </label>
            <input
              type="range"
              min="0"
              max="32"
              value={cidr}
              onChange={(e) => setCidr(parseInt(e.target.value))}
              className="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer"
            />
            <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
              <span>0</span>
              <span>16</span>
              <span>32</span>
            </div>
          </div>
        </div>

        {error && (
          <div className="mt-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-600 rounded-md flex items-center">
            <AlertCircle className="h-4 w-4 text-red-600 dark:text-red-400 mr-2" />
            <span className="text-red-800 dark:text-red-200">{error}</span>
          </div>
        )}
      </div>

      {/* Results */}
      {subnetInfo && (
        <div className="grid md:grid-cols-2 gap-6">
          {/* Basic Information */}
          <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">
              Subnet Information
            </h3>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-gray-600 dark:text-gray-400">
                  Network Class:
                </span>
                <span className="font-mono text-gray-900 dark:text-white">
                  {networkClass}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600 dark:text-gray-400">
                  Network Address:
                </span>
                <div className="flex items-center">
                  <span className="font-mono text-gray-900 dark:text-white mr-2">
                    {subnetInfo.networkAddress}
                  </span>
                  <button
                    onClick={() => copyToClipboard(subnetInfo.networkAddress)}
                    className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                    title="Copy to clipboard"
                  >
                    <Copy className="h-3 w-3" />
                  </button>
                </div>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600 dark:text-gray-400">
                  Broadcast Address:
                </span>
                <div className="flex items-center">
                  <span className="font-mono text-gray-900 dark:text-white mr-2">
                    {subnetInfo.broadcastAddress}
                  </span>
                  <button
                    onClick={() => copyToClipboard(subnetInfo.broadcastAddress)}
                    className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                    title="Copy to clipboard"
                  >
                    <Copy className="h-3 w-3" />
                  </button>
                </div>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600 dark:text-gray-400">
                  Subnet Mask:
                </span>
                <span className="font-mono text-gray-900 dark:text-white">
                  {subnetInfo.subnetMask}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600 dark:text-gray-400">
                  First Host:
                </span>
                <span className="font-mono text-gray-900 dark:text-white">
                  {subnetInfo.firstHost}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600 dark:text-gray-400">
                  Last Host:
                </span>
                <span className="font-mono text-gray-900 dark:text-white">
                  {subnetInfo.lastHost}
                </span>
              </div>
            </div>
          </div>

          {/* Host Information */}
          <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">
              Host Information
            </h3>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-gray-600 dark:text-gray-400">
                  Total Hosts:
                </span>
                <span className="font-mono text-gray-900 dark:text-white">
                  {subnetInfo.totalHosts.toLocaleString()}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600 dark:text-gray-400">
                  Usable Hosts:
                </span>
                <span className="font-mono text-gray-900 dark:text-white">
                  {subnetInfo.usableHosts.toLocaleString()}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600 dark:text-gray-400">
                  Subnet Bits:
                </span>
                <span className="font-mono text-gray-900 dark:text-white">
                  {subnetInfo.subnetBits}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600 dark:text-gray-400">
                  Host Bits:
                </span>
                <span className="font-mono text-gray-900 dark:text-white">
                  {subnetInfo.hostBits}
                </span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Binary Representation */}
      {subnetInfo && (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">
            Binary Representation
          </h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Network Address (Binary)
              </label>
              <div className="font-mono text-sm bg-gray-50 dark:bg-gray-700 p-3 rounded border">
                {subnetInfo.binaryNetwork}
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Subnet Mask (Binary)
              </label>
              <div className="font-mono text-sm bg-gray-50 dark:bg-gray-700 p-3 rounded border">
                {subnetInfo.binaryMask}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
