/* Basic CRUD Operations CSS Styles */
/* This file contains all the CSS styling for CRUD operations */
/* Students can link this file to their PHP pages for better presentation */

/* Table Styling */
table {
    width: 80%;
    border-collapse: collapse;
    margin: 20px 0;
    font-family: Arial, sans-serif;
}

table, th, td {
    border: 1px solid #ddd;
}

th, td {
    padding: 12px;
    text-align: left;
}

th {
    background-color: #f2f2f2;
    font-weight: bold;
}

/* Alternating row colors for better readability */
tr:nth-child(even) {
    background-color: #f9f9f9;
}

tr:hover {
    background-color: #f5f5f5;
}

/* Form Styling */
form {
    max-width: 600px;
    margin: 20px 0;
}

fieldset {
    border: 2px solid #ddd;
    border-radius: 5px;
    padding: 20px;
    margin: 10px 0;
}

legend {
    font-weight: bold;
    padding: 0 10px;
    color: #333;
}

input[type="text"], 
input[type="email"], 
input[type="number"] {
    width: 100%;
    padding: 8px;
    margin: 5px 0 15px 0;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-sizing: border-box;
}

input[type="radio"] {
    margin: 5px 10px 5px 0;
}

input[type="submit"] {
    background-color: #4CAF50;
    color: white;
    padding: 12px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    margin-right: 10px;
}

input[type="submit"]:hover {
    background-color: #45a049;
}

/* Navigation Links */
a {
    color: #007bff;
    text-decoration: none;
    padding: 8px 12px;
    margin: 5px;
    border: 1px solid #007bff;
    border-radius: 4px;
    display: inline-block;
}

a:hover {
    background-color: #007bff;
    color: white;
}

/* Page Headers */
h1 {
    color: #333;
    font-family: Arial, sans-serif;
    margin-bottom: 20px;
}

/* Success/Error Messages */
.success-message {
    color: #155724;
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    padding: 10px;
    border-radius: 4px;
    margin: 10px 0;
}

.error-message {
    color: #721c24;
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    padding: 10px;
    border-radius: 4px;
    margin: 10px 0;
}

/* Container for better layout */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Navigation buttons container */
.nav-buttons {
    margin: 20px 0;
}

.nav-buttons a {
    margin-right: 10px;
    margin-bottom: 10px;
}
