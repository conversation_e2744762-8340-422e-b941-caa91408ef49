"use client";

import { useState } from "react";
import { Copy } from "lucide-react";
import { Prism as Syntax<PERSON>ighlighter } from "react-syntax-highlighter";
import { vscDarkPlus } from "react-syntax-highlighter/dist/esm/styles/prism";

interface CodeBlockProps {
  code: string;
  title?: string;
  description?: string;
  language?: string;
}

export default function CodeBlock({
  code,
  title = "SQL",
  description,
  language = "sql",
}: CodeBlockProps) {
  const [copied, setCopied] = useState(false);

  const copyToClipboard = async () => {
    try {
      // First try the modern clipboard API
      if (navigator.clipboard && window.isSecureContext) {
        await navigator.clipboard.writeText(code);
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
      } else {
        // Fallback for older browsers or non-secure contexts
        fallbackCopyTextToClipboard(code);
      }
    } catch (err) {
      console.error("Failed to copy text: ", err);
      // Try fallback method if modern API fails
      fallbackCopyTextToClipboard(code);
    }
  };

  const fallbackCopyTextToClipboard = (text: string) => {
    try {
      const textArea = document.createElement("textarea");
      textArea.value = text;

      // Avoid scrolling to bottom
      textArea.style.top = "0";
      textArea.style.left = "0";
      textArea.style.position = "fixed";
      textArea.style.opacity = "0";

      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();

      const successful = document.execCommand("copy");
      document.body.removeChild(textArea);

      if (successful) {
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
      } else {
        console.error("Fallback: Could not copy text");
      }
    } catch (err) {
      console.error("Fallback: Unable to copy", err);
    }
  };

  // Determine if we should show line numbers (for longer code blocks)
  const shouldShowLineNumbers = code.split("\n").length > 10;

  return (
    <div className="my-8">
      {/* Title and Description */}
      <div className="mb-4">
        <h4 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
          {title}
        </h4>
        {description && (
          <div className="text-base text-gray-600 dark:text-gray-400 space-y-3">
            {description.split("\n\n").map((paragraph, index) => (
              <div key={index}>
                {paragraph.split("\n").map((line, lineIndex) => {
                  // Handle bold text **text**
                  const formattedLine = line.replace(
                    /\*\*(.*?)\*\*/g,
                    "<strong>$1</strong>"
                  );

                  // Check if it's a numbered list item
                  if (/^\d+\./.test(line.trim())) {
                    return (
                      <div key={lineIndex} className="ml-4 mb-1">
                        <span
                          dangerouslySetInnerHTML={{ __html: formattedLine }}
                        />
                      </div>
                    );
                  }

                  // Check if it's a step header (starts with **)
                  if (line.includes("**") && line.trim().startsWith("**")) {
                    return (
                      <div
                        key={lineIndex}
                        className="font-semibold text-gray-800 dark:text-gray-200 mt-4 mb-2"
                      >
                        <span
                          dangerouslySetInnerHTML={{ __html: formattedLine }}
                        />
                      </div>
                    );
                  }

                  // Regular line
                  if (line.trim()) {
                    return (
                      <div key={lineIndex} className="mb-1">
                        <span
                          dangerouslySetInnerHTML={{ __html: formattedLine }}
                        />
                      </div>
                    );
                  }

                  return null;
                })}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Code Block */}
      <div className="relative group w-full">
        <div className="absolute right-4 top-4 z-10">
          <button
            onClick={copyToClipboard}
            className="flex items-center gap-2 px-3 py-2 text-sm font-medium text-gray-400 hover:text-gray-200 bg-gray-800/90 hover:bg-gray-700/90 rounded-lg border border-gray-600/50 transition-all duration-200 opacity-0 group-hover:opacity-100 shadow-lg"
          >
            <Copy className="h-4 w-4" />
            {copied ? "Copied!" : "Copy"}
          </button>
        </div>

        <div className="rounded-xl border border-gray-700/50 overflow-hidden shadow-lg">
          <SyntaxHighlighter
            language={language}
            style={vscDarkPlus}
            customStyle={{
              margin: 0,
              padding: "24px",
              fontSize: "16px",
              lineHeight: "1.6",
              background: "#1e1e1e",
              borderRadius: "0",
            }}
            showLineNumbers={shouldShowLineNumbers}
            lineNumberStyle={{
              color: "#6b7280",
              paddingRight: "16px",
              fontSize: "14px",
              minWidth: "40px",
            }}
            wrapLines={true}
            wrapLongLines={true}
          >
            {code}
          </SyntaxHighlighter>
        </div>
      </div>
    </div>
  );
}
