"use client";

import { useState } from "react";
import Link from "next/link";
import { ArrowLeft, Calculator, Network } from "lucide-react";

export default function SubnettingGuidePage() {
  const [activeTab, setActiveTab] = useState("classA");

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <Link
          href="/calculator"
          className="inline-flex items-center text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 mb-4"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Calculator Hub
        </Link>
        <h1 className="text-4xl font-bold text-gray-900 dark:text-white">
          Subnetting Guide
        </h1>
        <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
          Learn subnetting with practical examples for Class A, B, and C
          networks. Calculate Network Address, Total Hosts, Usable Hosts, Subnet
          Mask, and Broadcast Address.
        </p>
      </div>

      {/* Tab Navigation */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="flex">
            <button
              onClick={() => setActiveTab("classA")}
              className={`px-6 py-4 text-sm font-medium border-b-2 transition-colors duration-200 ${
                activeTab === "classA"
                  ? "border-blue-500 text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20"
                  : "border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600"
              }`}
            >
              Class A Example
            </button>
            <button
              onClick={() => setActiveTab("classB")}
              className={`px-6 py-4 text-sm font-medium border-b-2 transition-colors duration-200 ${
                activeTab === "classB"
                  ? "border-green-500 text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-900/20"
                  : "border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600"
              }`}
            >
              Class B Example
            </button>
            <button
              onClick={() => setActiveTab("classC")}
              className={`px-6 py-4 text-sm font-medium border-b-2 transition-colors duration-200 ${
                activeTab === "classC"
                  ? "border-purple-500 text-purple-600 dark:text-purple-400 bg-purple-50 dark:bg-purple-900/20"
                  : "border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600"
              }`}
            >
              Class C Example
            </button>
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {/* Class A Example */}
          {activeTab === "classA" && (
            <div className="space-y-6">
              <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-600 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-3 flex items-center">
                  <Network className="h-5 w-5 mr-2" />
                  Class A Network Example: 10.0.0.0/16
                </h3>
                <p className="text-blue-800 dark:text-blue-200 text-sm mb-4">
                  Given IP: 10.0.0.0 with subnet mask /16 (***********)
                </p>
              </div>

              <div className="grid md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 className="font-semibold text-gray-900 dark:text-white mb-3">
                      📊 Calculations
                    </h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">
                          Host bits:
                        </span>
                        <span className="font-mono text-gray-900 dark:text-white">
                          32 - 16 = 16 bits
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">
                          Total hosts:
                        </span>
                        <span className="font-mono text-gray-900 dark:text-white">
                          2^16 = 65,536
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">
                          Usable hosts:
                        </span>
                        <span className="font-mono text-gray-900 dark:text-white">
                          65,536 - 2 = 65,534
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 className="font-semibold text-gray-900 dark:text-white mb-3">
                      🎯 Results
                    </h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">
                          Network Address:
                        </span>
                        <span className="font-mono text-blue-600 dark:text-blue-400 font-semibold">
                          10.0.0.0
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">
                          Subnet Mask:
                        </span>
                        <span className="font-mono text-blue-600 dark:text-blue-400 font-semibold">
                          ***********
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">
                          Broadcast Address:
                        </span>
                        <span className="font-mono text-blue-600 dark:text-blue-400 font-semibold">
                          ************
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">
                          Total Hosts:
                        </span>
                        <span className="font-mono text-blue-600 dark:text-blue-400 font-semibold">
                          65,536
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">
                          Usable Hosts:
                        </span>
                        <span className="font-mono text-blue-600 dark:text-blue-400 font-semibold">
                          65,534
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-600 rounded-lg p-4">
                <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
                  💡 Explanation:
                </h4>
                <p className="text-sm text-blue-800 dark:text-blue-200">
                  Class A networks have a default /8 mask, but this example uses
                  /16. The network portion is 10.0, and the host portion is 0.0
                  to 255.255. The first usable host is ******** and the last is
                  ************.
                </p>
              </div>
            </div>
          )}

          {/* Class B Example */}
          {activeTab === "classB" && (
            <div className="space-y-6">
              <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-600 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-green-900 dark:text-green-100 mb-3 flex items-center">
                  <Network className="h-5 w-5 mr-2" />
                  Class B Network Example: **********/20
                </h3>
                <p className="text-green-800 dark:text-green-200 text-sm mb-4">
                  Given IP: ********** with subnet mask /20 (*************)
                </p>
              </div>

              <div className="grid md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 className="font-semibold text-gray-900 dark:text-white mb-3">
                      📊 Calculations
                    </h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">
                          Host bits:
                        </span>
                        <span className="font-mono text-gray-900 dark:text-white">
                          32 - 20 = 12 bits
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">
                          Total hosts:
                        </span>
                        <span className="font-mono text-gray-900 dark:text-white">
                          2^12 = 4,096
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">
                          Usable hosts:
                        </span>
                        <span className="font-mono text-gray-900 dark:text-white">
                          4,096 - 2 = 4,094
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 className="font-semibold text-gray-900 dark:text-white mb-3">
                      🎯 Results
                    </h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">
                          Network Address:
                        </span>
                        <span className="font-mono text-green-600 dark:text-green-400 font-semibold">
                          **********
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">
                          Subnet Mask:
                        </span>
                        <span className="font-mono text-green-600 dark:text-green-400 font-semibold">
                          *************
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">
                          Broadcast Address:
                        </span>
                        <span className="font-mono text-green-600 dark:text-green-400 font-semibold">
                          *************
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">
                          Total Hosts:
                        </span>
                        <span className="font-mono text-green-600 dark:text-green-400 font-semibold">
                          4,096
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">
                          Usable Hosts:
                        </span>
                        <span className="font-mono text-green-600 dark:text-green-400 font-semibold">
                          4,094
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-600 rounded-lg p-4">
                <h4 className="font-medium text-green-900 dark:text-green-100 mb-2">
                  💡 Explanation:
                </h4>
                <p className="text-sm text-green-800 dark:text-green-200">
                  Class B networks have a default /16 mask, but this example
                  uses /20. The network portion is 172.16.0, and the host
                  portion ranges from 0.0 to 15.255. The first usable host is
                  ********** and the last is *************.
                </p>
              </div>
            </div>
          )}

          {/* Class C Example */}
          {activeTab === "classC" && (
            <div className="space-y-6">
              <div className="bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-600 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-purple-900 dark:text-purple-100 mb-3 flex items-center">
                  <Network className="h-5 w-5 mr-2" />
                  Class C Network Example: ***********/26
                </h3>
                <p className="text-purple-800 dark:text-purple-200 text-sm mb-4">
                  Given IP: *********** with subnet mask /26 (***************)
                </p>
              </div>

              <div className="grid md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 className="font-semibold text-gray-900 dark:text-white mb-3">
                      📊 Calculations
                    </h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">
                          Host bits:
                        </span>
                        <span className="font-mono text-gray-900 dark:text-white">
                          32 - 26 = 6 bits
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">
                          Total hosts:
                        </span>
                        <span className="font-mono text-gray-900 dark:text-white">
                          2^6 = 64
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">
                          Usable hosts:
                        </span>
                        <span className="font-mono text-gray-900 dark:text-white">
                          64 - 2 = 62
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 className="font-semibold text-gray-900 dark:text-white mb-3">
                      🎯 Results
                    </h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">
                          Network Address:
                        </span>
                        <span className="font-mono text-purple-600 dark:text-purple-400 font-semibold">
                          ***********
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">
                          Subnet Mask:
                        </span>
                        <span className="font-mono text-purple-600 dark:text-purple-400 font-semibold">
                          ***************
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">
                          Broadcast Address:
                        </span>
                        <span className="font-mono text-purple-600 dark:text-purple-400 font-semibold">
                          ************
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">
                          Total Hosts:
                        </span>
                        <span className="font-mono text-purple-600 dark:text-purple-400 font-semibold">
                          64
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">
                          Usable Hosts:
                        </span>
                        <span className="font-mono text-purple-600 dark:text-purple-400 font-semibold">
                          62
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-600 rounded-lg p-4">
                <h4 className="font-medium text-purple-900 dark:text-purple-100 mb-2">
                  💡 Explanation:
                </h4>
                <p className="text-sm text-purple-800 dark:text-purple-200">
                  Class C networks have a default /24 mask, but this example
                  uses /26. The network portion is ***********, and the host
                  portion ranges from 0 to 63. The first usable host is
                  *********** and the last is ************.
                </p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Calculation Methodology */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <h2 className="text-xl font-semibold mb-6 text-gray-900 dark:text-white flex items-center">
          <Calculator className="h-5 w-5 mr-2 text-orange-600 dark:text-orange-400" />
          How the Calculations Work
        </h2>

        <div className="space-y-8">
          {/* Step-by-step explanation */}
          <div className="bg-gray-50 dark:bg-gray-700 p-6 rounded-lg">
            {activeTab === "classA" && (
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                📚 Mathematical Process (Using Class A Example: 10.0.0.0/16)
              </h3>
            )}
            {activeTab === "classB" && (
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                📚 Mathematical Process (Using Class B Example: **********/20)
              </h3>
            )}
            {activeTab === "classC" && (
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                📚 Mathematical Process (Using Class C Example: ***********/26)
              </h3>
            )}

            <div className="space-y-6">
              {/* Step 1: Convert IP to Binary */}
              <div className="border-l-4 border-blue-500 pl-4">
                <h4 className="font-semibold text-gray-900 dark:text-white mb-2">
                  Step 1: Convert IP Address to 32-bit Binary
                </h4>
                <div className="space-y-2 text-sm">
                  <div className="font-mono bg-white dark:bg-gray-800 p-3 rounded border">
                    {activeTab === "classA" && (
                      <>
                        <div>10.0.0.0 = ?</div>
                        <div className="mt-2 text-blue-600 dark:text-blue-400">
                          10 = 00001010 (8+2 = 10)
                          <br />
                          0 = 00000000
                          <br />
                          0 = 00000000
                          <br />0 = 00000000
                        </div>
                        <div className="mt-2 font-bold">
                          Full Binary: 00001010.00000000.00000000.00000000
                        </div>
                      </>
                    )}
                    {activeTab === "classB" && (
                      <>
                        <div>********** = ?</div>
                        <div className="mt-2 text-blue-600 dark:text-blue-400">
                          172 = 10101100 (128+32+8+4 = 172)
                          <br />
                          16 = 00010000 (16 = 16)
                          <br />
                          0 = 00000000
                          <br />0 = 00000000
                        </div>
                        <div className="mt-2 font-bold">
                          Full Binary: 10101100.00010000.00000000.00000000
                        </div>
                      </>
                    )}
                    {activeTab === "classC" && (
                      <>
                        <div>*********** = ?</div>
                        <div className="mt-2 text-blue-600 dark:text-blue-400">
                          192 = 11000000 (128+64 = 192)
                          <br />
                          168 = 10101000 (128+32+8 = 168)
                          <br />
                          1 = 00000001
                          <br />0 = 00000000
                        </div>
                        <div className="mt-2 font-bold">
                          Full Binary: 11000000.10101000.00000001.00000000
                        </div>
                      </>
                    )}
                  </div>
                </div>
              </div>

              {/* Step 2: Create Subnet Mask */}
              <div className="border-l-4 border-green-500 pl-4">
                <h4 className="font-semibold text-gray-900 dark:text-white mb-2">
                  {activeTab === "classA" &&
                    "Step 2: Create Subnet Mask from CIDR /16"}
                  {activeTab === "classB" &&
                    "Step 2: Create Subnet Mask from CIDR /20"}
                  {activeTab === "classC" &&
                    "Step 2: Create Subnet Mask from CIDR /26"}
                </h4>
                <div className="space-y-2 text-sm">
                  <div className="font-mono bg-white dark:bg-gray-800 p-3 rounded border">
                    {activeTab === "classA" && (
                      <>
                        <div>
                          /16 means first 16 bits are 1s, remaining 16 bits are
                          0s
                        </div>
                        <div className="mt-2 text-green-600 dark:text-green-400">
                          11111111.11111111.00000000.00000000
                          <br />↑ 16 ones ↑ ↑ 16 zeros ↑
                        </div>
                        <div className="mt-2">
                          Convert to decimal:
                          <br />
                          11111111 = 255
                          <br />
                          11111111 = 255
                          <br />
                          00000000 = 0
                          <br />
                          00000000 = 0
                        </div>
                        <div className="mt-2 font-bold text-green-600 dark:text-green-400">
                          Subnet Mask: ***********
                        </div>
                      </>
                    )}
                    {activeTab === "classB" && (
                      <>
                        <div>
                          /20 means first 20 bits are 1s, remaining 12 bits are
                          0s
                        </div>
                        <div className="mt-2 text-green-600 dark:text-green-400">
                          11111111.11111111.11110000.00000000
                          <br />↑ 20 ones ↑ ↑ 12 zeros ↑
                        </div>
                        <div className="mt-2">
                          Convert to decimal:
                          <br />
                          11111111 = 255
                          <br />
                          11111111 = 255
                          <br />
                          11110000 = 240 (128+64+32+16 = 240)
                          <br />
                          00000000 = 0
                        </div>
                        <div className="mt-2 font-bold text-green-600 dark:text-green-400">
                          Subnet Mask: *************
                        </div>
                      </>
                    )}
                    {activeTab === "classC" && (
                      <>
                        <div>
                          /26 means first 26 bits are 1s, remaining 6 bits are
                          0s
                        </div>
                        <div className="mt-2 text-green-600 dark:text-green-400">
                          11111111.11111111.11111111.11000000
                          <br />↑ 26 ones ↑ ↑ 6 zeros ↑
                        </div>
                        <div className="mt-2">
                          Convert to decimal:
                          <br />
                          11111111 = 255
                          <br />
                          11111111 = 255
                          <br />
                          11111111 = 255
                          <br />
                          11000000 = 192 (128+64 = 192)
                        </div>
                        <div className="mt-2 font-bold text-green-600 dark:text-green-400">
                          Subnet Mask: ***************
                        </div>
                      </>
                    )}
                  </div>
                </div>
              </div>

              {/* Step 3: Calculate Network Address */}
              <div className="border-l-4 border-purple-500 pl-4">
                <h4 className="font-semibold text-gray-900 dark:text-white mb-2">
                  Step 3: Calculate Network Address (IP AND Subnet Mask)
                </h4>
                <div className="space-y-2 text-sm">
                  <div className="font-mono bg-white dark:bg-gray-800 p-3 rounded border">
                    <div>Perform bitwise AND operation:</div>
                    {activeTab === "classA" && (
                      <>
                        <div className="mt-2">
                          IP: 00001010.00000000.00000000.00000000
                          <br />
                          Mask: 11111111.11111111.00000000.00000000
                          <br />
                          ────────────────────────────────────────────
                          <br />
                          <span className="text-purple-600 dark:text-purple-400 font-bold">
                            Result: 00001010.00000000.00000000.00000000
                          </span>
                        </div>
                        <div className="mt-2">
                          Convert back to decimal:
                          <br />
                          00001010 = 10
                          <br />
                          00000000 = 0
                          <br />
                          00000000 = 0
                          <br />
                          00000000 = 0
                        </div>
                        <div className="mt-2 font-bold text-purple-600 dark:text-purple-400">
                          Network Address: 10.0.0.0
                        </div>
                      </>
                    )}
                    {activeTab === "classB" && (
                      <>
                        <div className="mt-2">
                          IP: 10101100.00010000.00000000.00000000
                          <br />
                          Mask: 11111111.11111111.11110000.00000000
                          <br />
                          ────────────────────────────────────────────
                          <br />
                          <span className="text-purple-600 dark:text-purple-400 font-bold">
                            Result: 10101100.00010000.00000000.00000000
                          </span>
                        </div>
                        <div className="mt-2">
                          Convert back to decimal:
                          <br />
                          10101100 = 172
                          <br />
                          00010000 = 16
                          <br />
                          00000000 = 0
                          <br />
                          00000000 = 0
                        </div>
                        <div className="mt-2 font-bold text-purple-600 dark:text-purple-400">
                          Network Address: **********
                        </div>
                      </>
                    )}
                    {activeTab === "classC" && (
                      <>
                        <div className="mt-2">
                          IP: 11000000.10101000.00000001.00000000
                          <br />
                          Mask: 11111111.11111111.11111111.11000000
                          <br />
                          ────────────────────────────────────────────
                          <br />
                          <span className="text-purple-600 dark:text-purple-400 font-bold">
                            Result: 11000000.10101000.00000001.00000000
                          </span>
                        </div>
                        <div className="mt-2">
                          Convert back to decimal:
                          <br />
                          11000000 = 192
                          <br />
                          10101000 = 168
                          <br />
                          00000001 = 1
                          <br />
                          00000000 = 0
                        </div>
                        <div className="mt-2 font-bold text-purple-600 dark:text-purple-400">
                          Network Address: ***********
                        </div>
                      </>
                    )}
                  </div>
                </div>
              </div>

              {/* Step 4: Calculate Broadcast Address */}
              <div className="border-l-4 border-red-500 pl-4">
                <h4 className="font-semibold text-gray-900 dark:text-white mb-2">
                  Step 4: Calculate Broadcast Address
                </h4>
                <div className="space-y-2 text-sm">
                  <div className="font-mono bg-white dark:bg-gray-800 p-3 rounded border">
                    {activeTab === "classA" && (
                      <>
                        <div>Set all host bits (last 16 bits) to 1:</div>
                        <div className="mt-2">
                          Network: 00001010.00000000.00000000.00000000
                          <br />
                          Host bits (last 16): ................11111111.11111111
                          <br />
                          <span className="text-red-600 dark:text-red-400 font-bold">
                            Broadcast: 00001010.00000000.11111111.11111111
                          </span>
                        </div>
                        <div className="mt-2">
                          Convert to decimal:
                          <br />
                          00001010 = 10
                          <br />
                          00000000 = 0
                          <br />
                          11111111 = 255
                          <br />
                          11111111 = 255
                        </div>
                        <div className="mt-2 font-bold text-red-600 dark:text-red-400">
                          Broadcast Address: ************
                        </div>
                      </>
                    )}
                    {activeTab === "classB" && (
                      <>
                        <div>Set all host bits (last 12 bits) to 1:</div>
                        <div className="mt-2">
                          Network: 10101100.00010000.00000000.00000000
                          <br />
                          Host bits (last 12): ....................1111.11111111
                          <br />
                          <span className="text-red-600 dark:text-red-400 font-bold">
                            Broadcast: 10101100.00010000.00001111.11111111
                          </span>
                        </div>
                        <div className="mt-2">
                          Convert to decimal:
                          <br />
                          10101100 = 172
                          <br />
                          00010000 = 16
                          <br />
                          00001111 = 15 (8+4+2+1 = 15)
                          <br />
                          11111111 = 255
                        </div>
                        <div className="mt-2 font-bold text-red-600 dark:text-red-400">
                          Broadcast Address: *************
                        </div>
                      </>
                    )}
                    {activeTab === "classC" && (
                      <>
                        <div>Set all host bits (last 6 bits) to 1:</div>
                        <div className="mt-2">
                          Network: 11000000.10101000.00000001.00000000
                          <br />
                          Host bits (last 6):
                          ..............................111111
                          <br />
                          <span className="text-red-600 dark:text-red-400 font-bold">
                            Broadcast: 11000000.10101000.00000001.00111111
                          </span>
                        </div>
                        <div className="mt-2">
                          Convert to decimal:
                          <br />
                          11000000 = 192
                          <br />
                          10101000 = 168
                          <br />
                          00000001 = 1
                          <br />
                          00111111 = 63 (32+16+8+4+2+1 = 63)
                        </div>
                        <div className="mt-2 font-bold text-red-600 dark:text-red-400">
                          Broadcast Address: ************
                        </div>
                      </>
                    )}
                  </div>
                </div>
              </div>

              {/* Step 5: Calculate Host Counts */}
              <div className="border-l-4 border-yellow-500 pl-4">
                <h4 className="font-semibold text-gray-900 dark:text-white mb-2">
                  Step 5: Calculate Host Counts
                </h4>
                <div className="space-y-2 text-sm">
                  <div className="font-mono bg-white dark:bg-gray-800 p-3 rounded border">
                    {activeTab === "classA" && (
                      <>
                        <div>Host bits = 32 - CIDR = 32 - 16 = 16 bits</div>
                        <div className="mt-2">
                          Total possible addresses = 2^16 = 65,536
                          <br />
                          <span className="text-yellow-600 dark:text-yellow-400 font-bold">
                            Total Hosts: 65,536
                          </span>
                        </div>
                        <div className="mt-2">
                          Usable hosts = Total - 2 (subtract network &
                          broadcast)
                          <br />
                          <span className="text-yellow-600 dark:text-yellow-400 font-bold">
                            Usable Hosts: 65,536 - 2 = 65,534
                          </span>
                        </div>
                        <div className="mt-2 text-gray-600 dark:text-gray-400">
                          Range: ******** to ************
                          <br />
                          (10.0.0.0 = network, ************ = broadcast)
                        </div>
                      </>
                    )}
                    {activeTab === "classB" && (
                      <>
                        <div>Host bits = 32 - CIDR = 32 - 20 = 12 bits</div>
                        <div className="mt-2">
                          Total possible addresses = 2^12 = 4,096
                          <br />
                          <span className="text-yellow-600 dark:text-yellow-400 font-bold">
                            Total Hosts: 4,096
                          </span>
                        </div>
                        <div className="mt-2">
                          Usable hosts = Total - 2 (subtract network &
                          broadcast)
                          <br />
                          <span className="text-yellow-600 dark:text-yellow-400 font-bold">
                            Usable Hosts: 4,096 - 2 = 4,094
                          </span>
                        </div>
                        <div className="mt-2 text-gray-600 dark:text-gray-400">
                          Range: ********** to *************
                          <br />
                          (********** = network, ************* = broadcast)
                        </div>
                      </>
                    )}
                    {activeTab === "classC" && (
                      <>
                        <div>Host bits = 32 - CIDR = 32 - 26 = 6 bits</div>
                        <div className="mt-2">
                          Total possible addresses = 2^6 = 64
                          <br />
                          <span className="text-yellow-600 dark:text-yellow-400 font-bold">
                            Total Hosts: 64
                          </span>
                        </div>
                        <div className="mt-2">
                          Usable hosts = Total - 2 (subtract network &
                          broadcast)
                          <br />
                          <span className="text-yellow-600 dark:text-yellow-400 font-bold">
                            Usable Hosts: 64 - 2 = 62
                          </span>
                        </div>
                        <div className="mt-2 text-gray-600 dark:text-gray-400">
                          Range: *********** to ************
                          <br />
                          (*********** = network, ************ = broadcast)
                        </div>
                      </>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Quick Reference */}
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-600 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-3">
              🔧 Quick Reference Formulas
            </h3>
            <div className="grid md:grid-cols-2 gap-4 text-sm">
              <div className="space-y-2">
                <div className="font-semibold text-blue-800 dark:text-blue-200">
                  Binary Conversion:
                </div>
                <div className="font-mono text-blue-700 dark:text-blue-300">
                  128 | 64 | 32 | 16 | 8 | 4 | 2 | 1<br />
                  Add values where bit = 1
                </div>
              </div>
              <div className="space-y-2">
                <div className="font-semibold text-blue-800 dark:text-blue-200">
                  Key Calculations:
                </div>
                <div className="font-mono text-blue-700 dark:text-blue-300">
                  Host bits = 32 - CIDR
                  <br />
                  Total hosts = 2^(host bits)
                  <br />
                  Usable hosts = Total - 2
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
