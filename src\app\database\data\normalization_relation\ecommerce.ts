// E-commerce System Data
export const ecommerceData = {
  normalization: [
    {
      title: "📋  The Challenge - Try It Yourself!",
      description: `Here's an unnormalized e-commerce table. Can you figure out how to normalize it?
  
  **Original Messy Table:**
   ProductID | ProductName | Price | CustomerID | CustomerName | CustomerEmail | OrderID | OrderDate | Quantity
  
  
  **Problems to Identify:**
  • What information is being repeated?
  • How many separate tables should this become?
  • What would be the primary keys for each table?
  
  **Your Challenge:** Think about how you would split this into clean, organized tables before looking at the solution!`,
      language: "text",
    },
    {
      title: "💡 UI-Based Solution (phpMyAdmin Method)",
      description: `**Perfect! Here's how to normalize the e-commerce database using phpMyAdmin:**
  
  **Step 1: Open phpMyAdmin**
  1. Start XAMPP Control Panel
  2. Click "Start" for Apache and MySQL
  3. Click "Admin" next to MySQL to open phpMyAdmin
  4. Click "New" to create a new database
  5. Name it "ecommerce_system" and click "Create"
  
  **Step 2: Create Products Table**
  1. Click on your database name "ecommerce_system"
  2. Click "Create table"
  3. Table name: "tbl_products", Number of columns: 5
  4. Set up columns:
     - ProductID: INT, Primary Key, Auto Increment
     - ProductName: VARCHAR(255), NOT NULL
     - Price: DECIMAL(10,2), NOT NULL
     - Category: VARCHAR(100)
     - Stock: INT

  **Step 3: Create Customers Table**
  1. Click "Create table" again
  2. Table name: "tbl_customers", Number of columns: 5
  3. Set up columns:
     - CustomerID: INT, Primary Key, Auto Increment
     - CustomerName: VARCHAR(255), NOT NULL
     - CustomerEmail: VARCHAR(255), UNIQUE
     - Phone: VARCHAR(15)
     - Address: TEXT
  
  **Step 4: Create Orders Table**
  1. Click "Create table" again
  2. Table name: "tbl_orders", Number of columns: 4
  3. Set up columns:
     - OrderID: INT, Primary Key, Auto Increment
     - CustomerID: INT, Index
     - OrderDate: DATE, NOT NULL
     - TotalAmount: DECIMAL(10,2)

  **Step 5: Create Order Items Table**
  1. Click "Create table" again
  2. Table name: "tbl_order_items", Number of columns: 5
  3. Set up columns:
     - ItemID: INT, Primary Key, Auto Increment
     - OrderID: INT, Index
     - ProductID: INT, Index
     - Quantity: INT, NOT NULL
     - UnitPrice: DECIMAL(10,2), NOT NULL
  
  **Step 6: Set Up Relationships**
  1. Go to tbl_orders table → Set CustomerID to reference tbl_customers(CustomerID)
  2. Go to tbl_order_items table → Set OrderID to reference tbl_orders(OrderID)
  3. Go to tbl_order_items table → Set ProductID to reference tbl_products(ProductID)
  
  ✅ **Done! Your normalized database is ready!**`,
      code: ``,
      language: "text",
    },
    {
      title: "Normalization Guide - SQL Code Method",
      description: `**Complete SQL Code to Create Normalized E-commerce Database**
  
  Copy and paste this code into phpMyAdmin's SQL tab:`,
      code: `-- Step 1: Create Database
  CREATE DATABASE ecommerce_system;
  USE ecommerce_system;
  
  -- Step 2: Create Products Table
  CREATE TABLE tbl_products (
      ProductID INT PRIMARY KEY AUTO_INCREMENT,
      ProductName VARCHAR(255) NOT NULL,
      Price DECIMAL(10,2) NOT NULL,
      Category VARCHAR(100),
      Stock INT DEFAULT 0
  );
  
  -- Step 3: Create Customers Table
  CREATE TABLE tbl_customers (
      CustomerID INT PRIMARY KEY AUTO_INCREMENT,
      CustomerName VARCHAR(255) NOT NULL,
      CustomerEmail VARCHAR(255) UNIQUE,
      Phone VARCHAR(15),
      Address TEXT
  );
  
  -- Step 4: Create Orders Table
  CREATE TABLE tbl_orders (
      OrderID INT PRIMARY KEY AUTO_INCREMENT,
      CustomerID INT,
      OrderDate DATE NOT NULL,
      TotalAmount DECIMAL(10,2),
      FOREIGN KEY (CustomerID) REFERENCES tbl_customers(CustomerID)
  );

  -- Step 5: Create Order Items Table
  CREATE TABLE tbl_order_items (
      ItemID INT PRIMARY KEY AUTO_INCREMENT,
      OrderID INT,
      ProductID INT,
      Quantity INT NOT NULL,
      UnitPrice DECIMAL(10,2) NOT NULL,
      FOREIGN KEY (OrderID) REFERENCES tbl_orders(OrderID),
      FOREIGN KEY (ProductID) REFERENCES tbl_products(ProductID)
  );
  
  -- Step 6: Insert Sample Data
  INSERT INTO tbl_products (ProductName, Price, Category, Stock) VALUES
  ('Laptop Computer', 999.99, 'Electronics', 50),
  ('Wireless Mouse', 29.99, 'Electronics', 100),
  ('Office Chair', 199.99, 'Furniture', 25);

  INSERT INTO tbl_customers (CustomerName, CustomerEmail, Phone, Address) VALUES
  ('John Smith', '<EMAIL>', '555-0101', '123 Main St'),
  ('Jane Doe', '<EMAIL>', '555-0102', '456 Oak Ave'),
  ('Bob Wilson', '<EMAIL>', '555-0103', '789 Pine Rd');

  INSERT INTO tbl_orders (CustomerID, OrderDate, TotalAmount) VALUES
  (1, '2024-01-15', 1029.98),
  (2, '2024-01-16', 199.99),
  (1, '2024-01-17', 29.99);

  INSERT INTO tbl_order_items (OrderID, ProductID, Quantity, UnitPrice) VALUES
  (1, 1, 1, 999.99),
  (1, 2, 1, 29.99),
  (2, 3, 1, 199.99),
  (3, 2, 1, 29.99);`,
      language: "sql",
    },
  ],
  relationships: [
    {
      title: "Understanding E-commerce Relationships",
      description: `**Why do we need relationships in our e-commerce system?**
  
  In an online store, we have four main entities:
  - **Products** (what we sell)
  - **Customers** (who buy from us)
  - **Orders** (purchase transactions)
  - **Order Items** (specific products in each order)
  
  **The Relationships:**
  1. **Customers → Orders**: One customer can place multiple orders (1-to-Many)
  2. **Orders → Order Items**: One order can contain multiple products (1-to-Many)
  3. **Products → Order Items**: One product can be in multiple orders (1-to-Many)
  
  This way, we can easily track customer purchases, inventory, and order details!`,
      code: ``,
      language: "text",
    },
  ],
};
