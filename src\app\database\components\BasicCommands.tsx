"use client";

import { basicCommandsData } from "../data/basic_commands";
import CodeBlock from "./CodeBlock";

export default function BasicCommands() {
  return (
    <div className="space-y-4 sm:space-y-6">
      <h2 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 dark:text-white">
        Database Basic Commands
      </h2>

      <div className="space-y-8">
        <div className="space-y-4">
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
            📋 Essential SQL Commands
          </h3>
          <p className="text-base text-gray-600 dark:text-gray-400">
            Learn the fundamental SQL commands for database operations.
          </p>
        </div>

        {basicCommandsData.map((command, index) => (
          <CodeBlock
            key={index}
            title={command.title}
            description={command.description}
            code={command.code}
          />
        ))}
      </div>
    </div>
  );
}
