"use client";

import { useState, useEffect } from "react";
import { Network, ArrowLeft, Plus, Trash2 } from "lucide-react";
import Link from "next/link";

interface VLSMRequirement {
  name: string;
  hosts: number;
}

interface VLSMResult {
  name: string;
  network: string;
  range: string;
  broadcast: string;
  usableHosts: number;
  requiredHosts: number;
}

export default function VLSMCalculatorPage() {
  const [mounted, setMounted] = useState(false);
  const [baseNetwork, setBaseNetwork] = useState("***********/24");
  const [requirements, setRequirements] = useState<VLSMRequirement[]>([
    { name: "Sales", hosts: 50 },
    { name: "Engineering", hosts: 30 },
    { name: "Management", hosts: 10 },
    { name: "Point-to-Point", hosts: 2 },
  ]);

  useEffect(() => {
    setMounted(true);
  }, []);

  const calculateVLSM = (): VLSMResult[] => {
    try {
      // Parse base network
      const [baseIp] = baseNetwork.split("/");

      const results: VLSMResult[] = [];
      let currentNetwork = baseIp;

      // Sort requirements by host count (descending) for efficient allocation
      const sorted = [...requirements].sort((a, b) => b.hosts - a.hosts);

      for (const req of sorted) {
        // Calculate required subnet size
        const requiredHosts = req.hosts + 2; // Add network and broadcast
        const hostBits = Math.ceil(Math.log2(requiredHosts));
        const subnetBits = 32 - hostBits;

        // Calculate network details
        const ipParts = currentNetwork.split(".").map(Number);
        const ipInt =
          (ipParts[0] << 24) +
          (ipParts[1] << 16) +
          (ipParts[2] << 8) +
          ipParts[3];

        const maskInt = (0xffffffff << hostBits) >>> 0;
        const networkInt = (ipInt & maskInt) >>> 0;
        const broadcastInt = (networkInt | (0xffffffff >>> subnetBits)) >>> 0;

        const intToIp = (int: number) =>
          [
            (int >>> 24) & 0xff,
            (int >>> 16) & 0xff,
            (int >>> 8) & 0xff,
            int & 0xff,
          ].join(".");

        results.push({
          name: req.name,
          network: `${intToIp(networkInt)}/${subnetBits}`,
          range: `${intToIp(networkInt + 1)} - ${intToIp(broadcastInt - 1)}`,
          broadcast: intToIp(broadcastInt),
          usableHosts: Math.pow(2, hostBits) - 2,
          requiredHosts: req.hosts,
        });

        // Next network starts after current broadcast
        currentNetwork = intToIp(broadcastInt + 1);
      }

      return results;
    } catch {
      return [];
    }
  };

  const addRequirement = () => {
    setRequirements([...requirements, { name: "New Department", hosts: 10 }]);
  };

  const removeRequirement = (index: number) => {
    setRequirements(requirements.filter((_, i) => i !== index));
  };

  const updateRequirement = (
    index: number,
    field: keyof VLSMRequirement,
    value: string | number
  ) => {
    const newReqs = [...requirements];
    newReqs[index] = { ...newReqs[index], [field]: value };
    setRequirements(newReqs);
  };

  const vlsmResults = mounted ? calculateVLSM() : [];

  if (!mounted) {
    return (
      <div className="space-y-8">
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white">
            VLSM Calculator
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Variable Length Subnet Masking calculator for efficient IP
            allocation.
          </p>
        </div>
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
          <div className="animate-pulse">
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-4"></div>
            <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded mb-4"></div>
            <div className="space-y-2">
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <Link
          href="/calculator"
          className="inline-flex items-center text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 mb-4"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Calculator Hub
        </Link>
        <h1 className="text-4xl font-bold text-gray-900 dark:text-white">
          VLSM Calculator
        </h1>
        <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
          Variable Length Subnet Masking calculator for efficient IP address
          allocation based on host requirements.
        </p>
      </div>

      {/* Input Section */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white flex items-center">
          <Network className="h-5 w-5 mr-2 text-orange-600 dark:text-orange-400" />
          VLSM Configuration
        </h2>

        <div className="grid md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Base Network
            </label>
            <input
              type="text"
              value={baseNetwork}
              onChange={(e) => setBaseNetwork(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              suppressHydrationWarning
            />

            <div className="mt-4">
              <div className="flex items-center justify-between mb-2">
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Host Requirements
                </h4>
                <button
                  onClick={addRequirement}
                  className="inline-flex items-center px-2 py-1 text-xs bg-orange-600 text-white rounded hover:bg-orange-700"
                >
                  <Plus className="h-3 w-3 mr-1" />
                  Add
                </button>
              </div>
              {requirements.map((req, index) => (
                <div key={index} className="flex items-center space-x-2 mb-2">
                  <input
                    type="text"
                    value={req.name}
                    onChange={(e) =>
                      updateRequirement(index, "name", e.target.value)
                    }
                    className="flex-1 px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    suppressHydrationWarning
                  />
                  <input
                    type="number"
                    value={req.hosts}
                    onChange={(e) =>
                      updateRequirement(
                        index,
                        "hosts",
                        parseInt(e.target.value) || 0
                      )
                    }
                    className="w-20 px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    suppressHydrationWarning
                  />
                  {requirements.length > 1 && (
                    <button
                      onClick={() => removeRequirement(index)}
                      className="p-1 text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  )}
                </div>
              ))}
            </div>
          </div>

          <div>
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              VLSM Results
            </h4>
            <div className="space-y-2">
              {vlsmResults.map((result, index) => (
                <div
                  key={index}
                  className="bg-gray-50 dark:bg-gray-700 p-3 rounded border"
                >
                  <div className="font-medium text-gray-900 dark:text-white">
                    {result.name}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    Network: <span className="font-mono">{result.network}</span>
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    Range: <span className="font-mono">{result.range}</span>
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    Usable Hosts: {result.usableHosts} (Required:{" "}
                    {result.requiredHosts})
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
