"use client";

import { useState, useEffect, useCallback } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  Refresh<PERSON><PERSON>,
  Check<PERSON>ircle,
  XCircle,
  Target,
  Trophy,
  Brain,
} from "lucide-react";
import Link from "next/link";

interface SubnetProblem {
  networkAddress: string;
  cidr: number;
  correctAnswers: {
    networkAddress: string;
    totalHosts: number;
    usableHosts: number;
    subnetMask: string;
    broadcastAddress: string;
  };
}

interface UserAnswers {
  networkAddress: string;
  totalHosts: string;
  usableHosts: string;
  subnetMask: string;
  broadcastAddress: string;
}

interface ValidationResult {
  networkAddress: boolean;
  totalHosts: boolean;
  usableHosts: boolean;
  subnetMask: boolean;
  broadcastAddress: boolean;
}

export default function SubnetPracticePage() {
  const [mounted, setMounted] = useState(false);
  const [currentProblem, setCurrentProblem] = useState<SubnetProblem | null>(
    null
  );
  const [userAnswers, setUserAnswers] = useState<UserAnswers>({
    networkAddress: "",
    totalHosts: "",
    usableHosts: "",
    subnetMask: "",
    broadcastAddress: "",
  });
  const [validation, setValidation] = useState<ValidationResult | null>(null);
  const [score, setScore] = useState({ correct: 0, total: 0 });
  const [showAnswers, setShowAnswers] = useState(false);

  useEffect(() => {
    setMounted(true);
    // Call generateNewProblem after component mounts
    const initializeProblem = () => {
      const randomIP = (() => {
        const classes = [
          { range: [10, 10], mask: [8, 16] }, // Class A private
          { range: [172, 172], mask: [16, 24] }, // Class B private
          { range: [192, 192], mask: [24, 30] }, // Class C private
        ];

        const selectedClass =
          classes[Math.floor(Math.random() * classes.length)];
        const firstOctet = selectedClass.range[0];

        let secondOctet, thirdOctet;

        if (firstOctet === 10) {
          secondOctet = Math.floor(Math.random() * 256);
          thirdOctet = Math.floor(Math.random() * 256);
        } else if (firstOctet === 172) {
          secondOctet = 16 + Math.floor(Math.random() * 16); // 172.16-31.x.x
          thirdOctet = Math.floor(Math.random() * 256);
        } else {
          // 192.168.x.x
          secondOctet = 168;
          thirdOctet = Math.floor(Math.random() * 256);
        }

        const fourthOctet = 0; // Always start with network address

        return `${firstOctet}.${secondOctet}.${thirdOctet}.${fourthOctet}`;
      })();

      const cidr = 16 + Math.floor(Math.random() * 15);

      // Generate a random host IP within the subnet (not necessarily the network address)
      // Students will need to calculate the actual network address
      const ipParts = randomIP.split(".").map(Number);
      const ipInt =
        (ipParts[0] << 24) +
        (ipParts[1] << 16) +
        (ipParts[2] << 8) +
        ipParts[3];
      const maskInt = (0xffffffff << (32 - cidr)) >>> 0;
      const networkInt = (ipInt & maskInt) >>> 0;
      const broadcastInt = (networkInt | (0xffffffff >>> cidr)) >>> 0;

      // Generate a random IP within the subnet range (between network and broadcast)
      const hostRange = broadcastInt - networkInt;
      const randomOffset = Math.floor(Math.random() * (hostRange + 1));
      const problemIP = networkInt + randomOffset;

      // Convert to IP string for the problem
      const problemIPString = [
        (problemIP >>> 24) & 0xff,
        (problemIP >>> 16) & 0xff,
        (problemIP >>> 8) & 0xff,
        problemIP & 0xff,
      ].join(".");

      // Calculate correct answers based on the actual network

      const intToIp = (int: number) =>
        [
          (int >>> 24) & 0xff,
          (int >>> 16) & 0xff,
          (int >>> 8) & 0xff,
          int & 0xff,
        ].join(".");

      const hostBits = 32 - cidr;
      const totalHosts = Math.pow(2, hostBits);
      const usableHosts = totalHosts - 2;

      const correctAnswers = {
        networkAddress: intToIp(networkInt),
        totalHosts,
        usableHosts,
        subnetMask: intToIp(maskInt),
        broadcastAddress: intToIp(broadcastInt),
      };

      setCurrentProblem({
        networkAddress: problemIPString, // This is the IP given to students
        cidr,
        correctAnswers, // This contains the actual network address they need to find
      });
    };

    initializeProblem();
  }, []);

  // Generate random IP address
  const generateRandomIP = (): string => {
    const classes = [
      { range: [10, 10], mask: [8, 16] }, // Class A private
      { range: [172, 172], mask: [16, 24] }, // Class B private
      { range: [192, 192], mask: [24, 30] }, // Class C private
    ];

    const selectedClass = classes[Math.floor(Math.random() * classes.length)];
    const firstOctet = selectedClass.range[0];

    let secondOctet, thirdOctet;

    if (firstOctet === 10) {
      secondOctet = Math.floor(Math.random() * 256);
      thirdOctet = Math.floor(Math.random() * 256);
    } else if (firstOctet === 172) {
      secondOctet = 16 + Math.floor(Math.random() * 16); // 172.16-31.x.x
      thirdOctet = Math.floor(Math.random() * 256);
    } else {
      // 192.168.x.x
      secondOctet = 168;
      thirdOctet = Math.floor(Math.random() * 256);
    }

    const fourthOctet = 0; // Always start with network address

    return `${firstOctet}.${secondOctet}.${thirdOctet}.${fourthOctet}`;
  };

  // Generate random CIDR (subnet mask)
  const generateRandomCIDR = (): number => {
    // Generate CIDR between 16 and 30 for practical subnetting
    return 16 + Math.floor(Math.random() * 15);
  };

  // Calculate subnet information
  const calculateSubnetInfo = (ip: string, cidr: number) => {
    const ipParts = ip.split(".").map(Number);
    const ipInt =
      (ipParts[0] << 24) + (ipParts[1] << 16) + (ipParts[2] << 8) + ipParts[3];

    const maskInt = (0xffffffff << (32 - cidr)) >>> 0;
    const networkInt = (ipInt & maskInt) >>> 0;
    const broadcastInt = (networkInt | (0xffffffff >>> cidr)) >>> 0;

    const intToIp = (int: number) =>
      [
        (int >>> 24) & 0xff,
        (int >>> 16) & 0xff,
        (int >>> 8) & 0xff,
        int & 0xff,
      ].join(".");

    const hostBits = 32 - cidr;
    const totalHosts = Math.pow(2, hostBits);
    const usableHosts = totalHosts - 2;

    return {
      networkAddress: intToIp(networkInt),
      totalHosts,
      usableHosts,
      subnetMask: intToIp(maskInt),
      broadcastAddress: intToIp(broadcastInt),
    };
  };

  // Generate new problem
  const generateNewProblem = useCallback(() => {
    const randomIP = generateRandomIP();
    const cidr = generateRandomCIDR();

    // Generate a random host IP within the subnet (not necessarily the network address)
    // Students will need to calculate the actual network address
    const ipParts = randomIP.split(".").map(Number);
    const ipInt =
      (ipParts[0] << 24) + (ipParts[1] << 16) + (ipParts[2] << 8) + ipParts[3];
    const maskInt = (0xffffffff << (32 - cidr)) >>> 0;
    const networkInt = (ipInt & maskInt) >>> 0;
    const broadcastInt = (networkInt | (0xffffffff >>> cidr)) >>> 0;

    // Generate a random IP within the subnet range (between network and broadcast)
    const hostRange = broadcastInt - networkInt;
    const randomOffset = Math.floor(Math.random() * (hostRange + 1));
    const problemIP = networkInt + randomOffset;

    // Convert to IP string for the problem
    const problemIPString = [
      (problemIP >>> 24) & 0xff,
      (problemIP >>> 16) & 0xff,
      (problemIP >>> 8) & 0xff,
      problemIP & 0xff,
    ].join(".");

    // Calculate correct answers based on the actual network
    const networkAddress = [
      (networkInt >>> 24) & 0xff,
      (networkInt >>> 16) & 0xff,
      (networkInt >>> 8) & 0xff,
      networkInt & 0xff,
    ].join(".");

    const correctAnswers = calculateSubnetInfo(networkAddress, cidr);

    setCurrentProblem({
      networkAddress: problemIPString, // This is the IP given to students
      cidr,
      correctAnswers, // This contains the actual network address they need to find
    });

    // Reset form
    setUserAnswers({
      networkAddress: "",
      totalHosts: "",
      usableHosts: "",
      subnetMask: "",
      broadcastAddress: "",
    });
    setValidation(null);
    setShowAnswers(false);
  }, []);

  // Validate user answers
  const validateAnswers = () => {
    if (!currentProblem) return;

    const { correctAnswers } = currentProblem;

    const validation: ValidationResult = {
      networkAddress:
        userAnswers.networkAddress.trim() === correctAnswers.networkAddress,
      totalHosts:
        parseInt(userAnswers.totalHosts) === correctAnswers.totalHosts,
      usableHosts:
        parseInt(userAnswers.usableHosts) === correctAnswers.usableHosts,
      subnetMask: userAnswers.subnetMask.trim() === correctAnswers.subnetMask,
      broadcastAddress:
        userAnswers.broadcastAddress.trim() === correctAnswers.broadcastAddress,
    };

    setValidation(validation);
    setShowAnswers(true);

    // Update score
    const allCorrect = Object.values(validation).every(Boolean);
    setScore((prev) => ({
      correct: prev.correct + (allCorrect ? 1 : 0),
      total: prev.total + 1,
    }));
  };

  // Handle input change
  const handleInputChange = (field: keyof UserAnswers, value: string) => {
    setUserAnswers((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  if (!mounted || !currentProblem) {
    return (
      <div className="space-y-8">
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white">
            Subnet Practice Quiz
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Practice your subnetting skills with randomly generated problems.
          </p>
        </div>
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
          <div className="animate-pulse">
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-4"></div>
            <div className="space-y-3">
              <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded"></div>
              <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <Link
          href="/calculator"
          className="inline-flex items-center text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 mb-4"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Calculator Hub
        </Link>
        <h1 className="text-4xl font-bold text-gray-900 dark:text-white">
          Subnet Practice Quiz
        </h1>
        <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
          Test your subnetting knowledge with randomly generated problems.
          Calculate the subnet details and check your answers!
        </p>
      </div>

      {/* Score Display */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Trophy className="h-6 w-6 text-yellow-600 dark:text-yellow-400 mr-3" />
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              Your Score
            </h2>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold text-gray-900 dark:text-white">
              {score.correct}/{score.total}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              {score.total > 0
                ? Math.round((score.correct / score.total) * 100)
                : 0}
              % Accuracy
            </div>
          </div>
        </div>
      </div>

      {/* Problem Display */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center">
            <Brain className="h-6 w-6 text-purple-600 dark:text-purple-400 mr-3" />
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              Current Problem
            </h2>
          </div>
          <button
            onClick={generateNewProblem}
            className="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors duration-200"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            New Problem
          </button>
        </div>

        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-600 rounded-lg p-4 mb-6">
          <h3 className="text-lg font-medium text-blue-900 dark:text-blue-100 mb-2">
            Given Information:
          </h3>
          <div className="space-y-2">
            <div>
              <span className="text-blue-700 dark:text-blue-300 font-medium">
                IP Address:{" "}
              </span>
              <span className="text-xl font-mono font-bold text-blue-800 dark:text-blue-200">
                {currentProblem.networkAddress}
              </span>
            </div>
            <div>
              <span className="text-blue-700 dark:text-blue-300 font-medium">
                CIDR:{" "}
              </span>
              <span className="text-xl font-mono font-bold text-blue-800 dark:text-blue-200">
                /{currentProblem.cidr}
              </span>
            </div>
          </div>
        </div>

        <div className="grid md:grid-cols-2 gap-6">
          {/* Input Fields */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Calculate the following:
            </h3>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Network Address
              </label>
              <input
                type="text"
                value={userAnswers.networkAddress}
                onChange={(e) =>
                  handleInputChange("networkAddress", e.target.value)
                }
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="e.g., ***********"
                disabled={showAnswers}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Total Hosts
              </label>
              <input
                type="number"
                value={userAnswers.totalHosts}
                onChange={(e) =>
                  handleInputChange("totalHosts", e.target.value)
                }
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Enter total hosts"
                disabled={showAnswers}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Usable Hosts
              </label>
              <input
                type="number"
                value={userAnswers.usableHosts}
                onChange={(e) =>
                  handleInputChange("usableHosts", e.target.value)
                }
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Enter usable hosts"
                disabled={showAnswers}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Subnet Mask
              </label>
              <input
                type="text"
                value={userAnswers.subnetMask}
                onChange={(e) =>
                  handleInputChange("subnetMask", e.target.value)
                }
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="e.g., *************"
                disabled={showAnswers}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Broadcast Address
              </label>
              <input
                type="text"
                value={userAnswers.broadcastAddress}
                onChange={(e) =>
                  handleInputChange("broadcastAddress", e.target.value)
                }
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="e.g., *************"
                disabled={showAnswers}
              />
            </div>

            {!showAnswers && (
              <button
                onClick={validateAnswers}
                className="w-full inline-flex items-center justify-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-md transition-colors duration-200"
              >
                <Target className="h-4 w-4 mr-2" />
                Check Answers
              </button>
            )}
          </div>

          {/* Results Display */}
          <div className="space-y-4">
            {showAnswers && validation && (
              <>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                  Results:
                </h3>

                <div className="space-y-3">
                  <div
                    className={`p-3 rounded-lg border ${
                      validation.networkAddress
                        ? "border-green-200 bg-green-50 dark:bg-green-900/20 dark:border-green-600"
                        : "border-red-200 bg-red-50 dark:bg-red-900/20 dark:border-red-600"
                    }`}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        Network Address:
                      </span>
                      {validation.networkAddress ? (
                        <CheckCircle className="h-5 w-5 text-green-600" />
                      ) : (
                        <XCircle className="h-5 w-5 text-red-600" />
                      )}
                    </div>
                    <div className="space-y-1">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600 dark:text-gray-400">
                          Your answer:
                        </span>
                        <span
                          className={`font-mono ${
                            validation.networkAddress
                              ? "text-green-700 dark:text-green-300"
                              : "text-red-700 dark:text-red-300"
                          }`}
                        >
                          {userAnswers.networkAddress || "Not answered"}
                        </span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600 dark:text-gray-400">
                          Correct answer:
                        </span>
                        <span className="font-mono text-green-700 dark:text-green-300">
                          {currentProblem.correctAnswers.networkAddress}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div
                    className={`p-3 rounded-lg border ${
                      validation.totalHosts
                        ? "border-green-200 bg-green-50 dark:bg-green-900/20 dark:border-green-600"
                        : "border-red-200 bg-red-50 dark:bg-red-900/20 dark:border-red-600"
                    }`}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        Total Hosts:
                      </span>
                      {validation.totalHosts ? (
                        <CheckCircle className="h-5 w-5 text-green-600" />
                      ) : (
                        <XCircle className="h-5 w-5 text-red-600" />
                      )}
                    </div>
                    <div className="space-y-1">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600 dark:text-gray-400">
                          Your answer:
                        </span>
                        <span
                          className={`font-mono ${
                            validation.totalHosts
                              ? "text-green-700 dark:text-green-300"
                              : "text-red-700 dark:text-red-300"
                          }`}
                        >
                          {userAnswers.totalHosts || "Not answered"}
                        </span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600 dark:text-gray-400">
                          Correct answer:
                        </span>
                        <span className="font-mono text-green-700 dark:text-green-300">
                          {currentProblem.correctAnswers.totalHosts}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div
                    className={`p-3 rounded-lg border ${
                      validation.usableHosts
                        ? "border-green-200 bg-green-50 dark:bg-green-900/20 dark:border-green-600"
                        : "border-red-200 bg-red-50 dark:bg-red-900/20 dark:border-red-600"
                    }`}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        Usable Hosts:
                      </span>
                      {validation.usableHosts ? (
                        <CheckCircle className="h-5 w-5 text-green-600" />
                      ) : (
                        <XCircle className="h-5 w-5 text-red-600" />
                      )}
                    </div>
                    <div className="space-y-1">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600 dark:text-gray-400">
                          Your answer:
                        </span>
                        <span
                          className={`font-mono ${
                            validation.usableHosts
                              ? "text-green-700 dark:text-green-300"
                              : "text-red-700 dark:text-red-300"
                          }`}
                        >
                          {userAnswers.usableHosts || "Not answered"}
                        </span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600 dark:text-gray-400">
                          Correct answer:
                        </span>
                        <span className="font-mono text-green-700 dark:text-green-300">
                          {currentProblem.correctAnswers.usableHosts}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div
                    className={`p-3 rounded-lg border ${
                      validation.subnetMask
                        ? "border-green-200 bg-green-50 dark:bg-green-900/20 dark:border-green-600"
                        : "border-red-200 bg-red-50 dark:bg-red-900/20 dark:border-red-600"
                    }`}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        Subnet Mask:
                      </span>
                      {validation.subnetMask ? (
                        <CheckCircle className="h-5 w-5 text-green-600" />
                      ) : (
                        <XCircle className="h-5 w-5 text-red-600" />
                      )}
                    </div>
                    <div className="space-y-1">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600 dark:text-gray-400">
                          Your answer:
                        </span>
                        <span
                          className={`font-mono ${
                            validation.subnetMask
                              ? "text-green-700 dark:text-green-300"
                              : "text-red-700 dark:text-red-300"
                          }`}
                        >
                          {userAnswers.subnetMask || "Not answered"}
                        </span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600 dark:text-gray-400">
                          Correct answer:
                        </span>
                        <span className="font-mono text-green-700 dark:text-green-300">
                          {currentProblem.correctAnswers.subnetMask}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div
                    className={`p-3 rounded-lg border ${
                      validation.broadcastAddress
                        ? "border-green-200 bg-green-50 dark:bg-green-900/20 dark:border-green-600"
                        : "border-red-200 bg-red-50 dark:bg-red-900/20 dark:border-red-600"
                    }`}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        Broadcast Address:
                      </span>
                      {validation.broadcastAddress ? (
                        <CheckCircle className="h-5 w-5 text-green-600" />
                      ) : (
                        <XCircle className="h-5 w-5 text-red-600" />
                      )}
                    </div>
                    <div className="space-y-1">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600 dark:text-gray-400">
                          Your answer:
                        </span>
                        <span
                          className={`font-mono ${
                            validation.broadcastAddress
                              ? "text-green-700 dark:text-green-300"
                              : "text-red-700 dark:text-red-300"
                          }`}
                        >
                          {userAnswers.broadcastAddress || "Not answered"}
                        </span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600 dark:text-gray-400">
                          Correct answer:
                        </span>
                        <span className="font-mono text-green-700 dark:text-green-300">
                          {currentProblem.correctAnswers.broadcastAddress}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Overall Result */}
                <div
                  className={`p-4 rounded-lg border-2 ${
                    Object.values(validation).every(Boolean)
                      ? "border-green-200 bg-green-50 dark:bg-green-900/20 dark:border-green-600"
                      : "border-red-200 bg-red-50 dark:bg-red-900/20 dark:border-red-600"
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      {Object.values(validation).every(Boolean) ? (
                        <>
                          <CheckCircle className="h-6 w-6 text-green-600 mr-3" />
                          <div>
                            <div className="font-medium text-green-800 dark:text-green-200">
                              Perfect! All answers correct!
                            </div>
                            <div className="text-sm text-green-600 dark:text-green-300">
                              Great job on your subnetting skills!
                            </div>
                          </div>
                        </>
                      ) : (
                        <>
                          <XCircle className="h-6 w-6 text-red-600 mr-3" />
                          <div>
                            <div className="font-medium text-red-800 dark:text-red-200">
                              {Object.values(validation).filter(Boolean).length}
                              /5 answers correct
                            </div>
                            <div className="text-sm text-red-600 dark:text-red-300">
                              Incorrect:{" "}
                              {Object.entries(validation)
                                .filter(([, isCorrect]) => !isCorrect)
                                .map(([field]) =>
                                  field.replace(/([A-Z])/g, " $1").toLowerCase()
                                )
                                .join(", ")}
                            </div>
                          </div>
                        </>
                      )}
                    </div>

                    {/* Action Buttons */}
                    <div className="flex gap-2">
                      {!Object.values(validation).every(Boolean) && (
                        <button
                          onClick={() => {
                            setShowAnswers(false);
                            setValidation(null);
                          }}
                          className="px-3 py-1 text-sm bg-orange-600 hover:bg-orange-700 text-white rounded-md transition-colors duration-200"
                        >
                          Try Again
                        </button>
                      )}
                      <button
                        onClick={generateNewProblem}
                        className="px-3 py-1 text-sm bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors duration-200"
                      >
                        New Problem
                      </button>
                    </div>
                  </div>
                </div>

                {/* Calculation Explanation */}
                <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-600 rounded-lg p-4">
                  <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-3">
                    💡 How to Calculate:
                  </h4>
                  <div className="space-y-2 text-sm text-blue-800 dark:text-blue-200">
                    <div>
                      <strong>Given:</strong> IP {currentProblem.networkAddress}{" "}
                      with CIDR /{currentProblem.cidr}
                    </div>
                    <div>
                      <strong>Network address:</strong> Apply subnet mask to IP
                      = {currentProblem.correctAnswers.networkAddress}
                    </div>
                    <div>
                      <strong>Host bits:</strong> 32 - {currentProblem.cidr} ={" "}
                      {32 - currentProblem.cidr} bits
                    </div>
                    <div>
                      <strong>Total hosts:</strong> 2^{32 - currentProblem.cidr}{" "}
                      = {currentProblem.correctAnswers.totalHosts}
                    </div>
                    <div>
                      <strong>Usable hosts:</strong>{" "}
                      {currentProblem.correctAnswers.totalHosts} - 2 ={" "}
                      {currentProblem.correctAnswers.usableHosts} (subtract
                      network & broadcast)
                    </div>
                    <div>
                      <strong>Subnet mask:</strong> /{currentProblem.cidr} ={" "}
                      {currentProblem.correctAnswers.subnetMask}
                    </div>
                    <div>
                      <strong>Broadcast:</strong> Last address in subnet ={" "}
                      {currentProblem.correctAnswers.broadcastAddress}
                    </div>
                  </div>
                </div>
              </>
            )}

            {!showAnswers && (
              <div className="text-center py-8">
                <Target className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600 dark:text-gray-400">
                  Fill in your answers and click Check Answers to see the
                  results.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Tips Section */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
          💡 Quick Tips
        </h2>
        <div className="grid md:grid-cols-2 gap-4">
          <div>
            <h3 className="font-medium text-gray-900 dark:text-white mb-2">
              Calculating Hosts:
            </h3>
            <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
              <li>• Total Hosts = 2^(host bits)</li>
              <li>• Usable Hosts = Total Hosts - 2</li>
              <li>• Host bits = 32 - CIDR</li>
            </ul>
          </div>
          <div>
            <h3 className="font-medium text-gray-900 dark:text-white mb-2">
              Finding Addresses:
            </h3>
            <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
              <li>• Subnet mask: Apply CIDR to ***************</li>
              <li>• Broadcast: Last address in subnet</li>
              <li>• Network: First address in subnet</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
