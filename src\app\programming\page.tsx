"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON> } from "lucide-react";
import { Prism as SyntaxHighlighter } from "react-syntax-highlighter";
import { vscDarkPlus } from "react-syntax-highlighter/dist/esm/styles/prism";

// Code Block Component - React Docs Style
function CodeBlock({
  title,
  code,
  description,
}: {
  title: string;
  code: string;
  description?: string;
}) {
  const [copied, setCopied] = useState(false);

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(code);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error("Failed to copy text: ", err);
    }
  };

  // Determine if we should show line numbers (for longer code blocks)
  const shouldShowLineNumbers = code.split("\n").length > 10;

  return (
    <div className="my-8">
      {/* Title and Description */}
      <div className="mb-4">
        <h4 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
          {title}
        </h4>
        {description && (
          <p className="text-base text-gray-600 dark:text-gray-400">
            {description}
          </p>
        )}
      </div>

      {/* Code Block */}
      <div className="relative group w-full">
        <div className="absolute right-4 top-4 z-10">
          <button
            onClick={copyToClipboard}
            className="flex items-center gap-2 px-3 py-2 text-sm font-medium text-gray-400 hover:text-gray-200 bg-gray-800/90 hover:bg-gray-700/90 rounded-lg border border-gray-600/50 transition-all duration-200 opacity-0 group-hover:opacity-100 shadow-lg"
          >
            <Copy className="h-4 w-4" />
            {copied ? "Copied!" : "Copy"}
          </button>
        </div>

        <div className="rounded-xl border border-gray-700/50 overflow-hidden shadow-lg">
          <SyntaxHighlighter
            language="c"
            style={vscDarkPlus}
            customStyle={{
              margin: 0,
              padding: "24px",
              fontSize: "16px",
              lineHeight: "1.6",
              background: "#1e1e1e",
              borderRadius: "0",
            }}
            showLineNumbers={shouldShowLineNumbers}
            lineNumberStyle={{
              color: "#6b7280",
              paddingRight: "16px",
              fontSize: "14px",
              minWidth: "40px",
            }}
            wrapLines={true}
            wrapLongLines={true}
          >
            {code}
          </SyntaxHighlighter>
        </div>
      </div>
    </div>
  );
}

export default function ProgrammingPage() {
  const [activeTab, setActiveTab] = useState("basics");

  const tabs = [
    { id: "basics", label: "Basic Commands", icon: BookOpen },
    { id: "algorithms", label: "Algorithms", icon: Zap },
    { id: "patterns", label: "Star Patterns", icon: Star },
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 space-y-8">
        {/* Header */}
        <div className="text-center space-y-6">
          <h1 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white">
            C Programming Showcase
          </h1>
          <p className="text-lg sm:text-xl text-gray-600 dark:text-gray-300 max-w-4xl mx-auto leading-relaxed">
            Comprehensive code examples demonstrating fundamental algorithms and
            patterns with clean syntax highlighting and detailed explanations
            for learning.
          </p>
        </div>

        {/* Navigation Tabs */}
        <div className="flex flex-col sm:flex-row justify-center gap-3 sm:gap-2 p-2 bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center justify-center space-x-3 px-6 py-3 rounded-lg transition-all duration-200 font-medium ${
                  activeTab === tab.id
                    ? "bg-blue-600 text-white shadow-md transform scale-105"
                    : "text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700"
                }`}
              >
                <Icon className="h-5 w-5" />
                <span className="text-base">{tab.label}</span>
              </button>
            );
          })}
        </div>

        {/* Content */}
        <div className="space-y-8">
          {activeTab === "basics" && <BasicCommandsSection />}
          {activeTab === "algorithms" && <AlgorithmsSection />}
          {activeTab === "patterns" && <PatternsSection />}
        </div>
      </div>
    </div>
  );
}

// Algorithms Section
function AlgorithmsSection() {
  return (
    <div className="space-y-8">
      <div className="text-center space-y-4">
        <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white">
          Recursive Algorithms
        </h2>
        <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
          Learn fundamental recursive programming concepts with practical
          examples
        </p>
      </div>

      <div className="space-y-8">
        <CodeBlock
          title="1. Find Largest Among Three Numbers"
          description="Simple program to find the largest number among three given numbers"
          code={`#include <stdio.h>

int main() {
    int num1, num2, num3, largest;

    printf("Enter three numbers: ");
    scanf("%d %d %d", &num1, &num2, &num3);

    if (num1 >= num2 && num1 >= num3) {
        largest = num1;
    } else if (num2 >= num1 && num2 >= num3) {
        largest = num2;
    } else {
        largest = num3;
    }

    printf("The largest number is: %d\\n", largest);
    return 0;
}

/*
Example Output:
Enter three numbers: 15 8 23
The largest number is: 23
*/`}
        />

        <CodeBlock
          title="2. Prime Number Checker"
          description="Program to check whether a given number is prime or not"
          code={`#include <stdio.h>
#include <math.h>

int isPrime(int n) {
    if (n <= 1) {
        return 0; // Not prime
    }
    if (n <= 3) {
        return 1; // Prime
    }
    if (n % 2 == 0 || n % 3 == 0) {
        return 0; // Not prime
    }

    for (int i = 5; i * i <= n; i += 6) {
        if (n % i == 0 || n % (i + 2) == 0) {
            return 0; // Not prime
        }
    }
    return 1; // Prime
}

int main() {
    int num;

    printf("Enter a number: ");
    scanf("%d", &num);

    if (isPrime(num)) {
        printf("%d is a prime number.\\n", num);
    } else {
        printf("%d is not a prime number.\\n", num);
    }

    return 0;
}

/*
Example Output:
Enter a number: 17
17 is a prime number.
*/`}
        />

        <CodeBlock
          title="3. Binary to Decimal Converter"
          description="Program to convert binary number to decimal number"
          code={`#include <stdio.h>
#include <math.h>

int binaryToDecimal(long long binary) {
    int decimal = 0, i = 0, remainder;

    while (binary != 0) {
        remainder = binary % 10;
        binary /= 10;
        decimal += remainder * pow(2, i);
        ++i;
    }

    return decimal;
}

int main() {
    long long binary;

    printf("Enter a binary number: ");
    scanf("%lld", &binary);

    int decimal = binaryToDecimal(binary);
    printf("Binary %lld in decimal is: %d\\n", binary, decimal);

    return 0;
}

/*
Example Output:
Enter a binary number: 1101
Binary 1101 in decimal is: 13

Explanation: 1101₂ = 1×2³ + 1×2² + 0×2¹ + 1×2⁰ = 8 + 4 + 0 + 1 = 13
*/`}
        />

        <CodeBlock
          title="4. Leap Year Checker"
          description="Program to check if a given year is a leap year or not"
          code={`#include <stdio.h>

int isLeapYear(int year) {
    if (year % 400 == 0) {
        return 1; // Leap year
    } else if (year % 100 == 0) {
        return 0; // Not a leap year
    } else if (year % 4 == 0) {
        return 1; // Leap year
    } else {
        return 0; // Not a leap year
    }
}

int main() {
    int year;

    printf("Enter a year: ");
    scanf("%d", &year);

    if (isLeapYear(year)) {
        printf("%d is a leap year.\\n", year);
    } else {
        printf("%d is not a leap year.\\n", year);
    }

    return 0;
}

/*
Example Output:
Enter a year: 2024
2024 is a leap year.

Leap Year Rules:
- Divisible by 400: Leap year
- Divisible by 100 but not 400: Not leap year
- Divisible by 4 but not 100: Leap year
- Otherwise: Not leap year
*/`}
        />

        <CodeBlock
          title="5. Reverse a Number"
          description="Program to reverse the digits of a given number"
          code={`#include <stdio.h>

int reverseNumber(int num) {
    int reversed = 0;

    while (num != 0) {
        reversed = reversed * 10 + num % 10;
        num /= 10;
    }

    return reversed;
}

int main() {
    int num, reversed;

    printf("Enter a number: ");
    scanf("%d", &num);

    reversed = reverseNumber(num);
    printf("Original number: %d\\n", num);
    printf("Reversed number: %d\\n", reversed);

    return 0;
}

/*
Example Output:
Enter a number: 12345
Original number: 12345
Reversed number: 54321

Logic: Extract last digit using % 10, build reversed number
*/`}
        />

        <CodeBlock
          title="6. Palindrome Number Checker"
          description="Program to check if a number is a palindrome"
          code={`#include <stdio.h>

int isPalindrome(int num) {
    int original = num;
    int reversed = 0;

    while (num > 0) {
        reversed = reversed * 10 + num % 10;
        num /= 10;
    }

    return (original == reversed);
}

int main() {
    int num;

    printf("Enter a number: ");
    scanf("%d", &num);

    if (isPalindrome(num)) {
        printf("%d is a palindrome.\\n", num);
    } else {
        printf("%d is not a palindrome.\\n", num);
    }

    return 0;
}

/*
Example Output:
Enter a number: 12321
12321 is a palindrome.

A palindrome reads the same forwards and backwards
*/`}
        />

        <CodeBlock
          title="7. Reverse an Array"
          description="Program to reverse the elements of an array"
          code={`#include <stdio.h>

void reverseArray(int arr[], int size) {
    int start = 0;
    int end = size - 1;

    while (start < end) {
        // Swap elements
        int temp = arr[start];
        arr[start] = arr[end];
        arr[end] = temp;

        start++;
        end--;
    }
}

void printArray(int arr[], int size) {
    for (int i = 0; i < size; i++) {
        printf("%d ", arr[i]);
    }
    printf("\\n");
}

int main() {
    int arr[] = {1, 2, 3, 4, 5};
    int size = sizeof(arr) / sizeof(arr[0]);

    printf("Original array: ");
    printArray(arr, size);

    reverseArray(arr, size);

    printf("Reversed array: ");
    printArray(arr, size);

    return 0;
}

/*
Example Output:
Original array: 1 2 3 4 5
Reversed array: 5 4 3 2 1

Logic: Swap elements from both ends moving towards center
*/`}
        />

        <CodeBlock
          title="8. Calculate String Length"
          description="Program to calculate the length of a string without using strlen()"
          code={`#include <stdio.h>

int calculateLength(char str[]) {
    int length = 0;

    while (str[length] != '\\0') {
        length++;
    }

    return length;
}

int main() {
    char str[100];

    printf("Enter a string: ");
    scanf("%s", str);

    int length = calculateLength(str);
    printf("Length of '%s' is: %d\\n", str, length);

    return 0;
}

/*
Example Output:
Enter a string: Hello
Length of 'Hello' is: 5

Logic: Count characters until null terminator '\\0' is found
*/`}
        />

        <CodeBlock
          title="9. String Palindrome Checker"
          description="Program to check if a string is a palindrome"
          code={`#include <stdio.h>
#include <string.h>
#include <ctype.h>

int isStringPalindrome(char str[]) {
    int left = 0;
    int right = strlen(str) - 1;

    while (left < right) {
        // Convert to lowercase for case-insensitive comparison
        if (tolower(str[left]) != tolower(str[right])) {
            return 0; // Not a palindrome
        }
        left++;
        right--;
    }

    return 1; // Is a palindrome
}

int main() {
    char str[100];

    printf("Enter a string: ");
    scanf("%s", str);

    if (isStringPalindrome(str)) {
        printf("'%s' is a palindrome.\\n", str);
    } else {
        printf("'%s' is not a palindrome.\\n", str);
    }

    return 0;
}

/*
Example Output:
Enter a string: racecar
'racecar' is a palindrome.

Logic: Compare characters from both ends moving towards center
*/`}
        />

        <CodeBlock
          title="10. Fibonacci Series (Recursion)"
          description="Program to print Fibonacci series using recursion"
          code={`#include <stdio.h>

int fibonacci(int n) {
    if (n <= 1) {
        return n;
    }
    return fibonacci(n - 1) + fibonacci(n - 2);
}

void printFibonacci(int terms) {
    printf("Fibonacci Series: ");
    for (int i = 0; i < terms; i++) {
        printf("%d ", fibonacci(i));
    }
    printf("\\n");
}

int main() {
    int terms;

    printf("Enter number of terms: ");
    scanf("%d", &terms);

    if (terms <= 0) {
        printf("Please enter a positive number.\\n");
    } else {
        printFibonacci(terms);
    }

    return 0;
}

/*
Example Output:
Enter number of terms: 8
Fibonacci Series: 0 1 1 2 3 5 8 13

Logic: F(n) = F(n-1) + F(n-2), with F(0)=0, F(1)=1
*/`}
        />

        <CodeBlock
          title="11. Reverse String (Recursion)"
          description="Program to reverse a string using recursion"
          code={`#include <stdio.h>
#include <string.h>

void reverseString(char str[], int start, int end) {
    if (start >= end) {
        return; // Base case: reached middle or crossed over
    }

    // Swap characters at start and end positions
    char temp = str[start];
    str[start] = str[end];
    str[end] = temp;

    // Recursive call with updated positions
    reverseString(str, start + 1, end - 1);
}

int main() {
    char str[100];

    printf("Enter a string: ");
    scanf("%s", str);

    printf("Original string: %s\\n", str);

    int length = strlen(str);
    reverseString(str, 0, length - 1);

    printf("Reversed string: %s\\n", str);

    return 0;
}

/*
Example Output:
Enter a string: programming
Original string: programming
Reversed string: gnimmargorP

Logic: Recursively swap characters from both ends towards center
*/`}
        />
        <CodeBlock
          title="12 . Factorial Calculation"
          description="Classic recursive algorithm to calculate factorial of a number"
          code={`#include <stdio.h>

// Recursive function to calculate factorial
long long factorial(int n) {
    // Base case
    if (n == 0 || n == 1) {
        return 1;
    }

    // Recursive case
    return n * factorial(n - 1);
}

int main() {
    int num;

    printf("Enter a number: ");
    scanf("%d", &num);

    if (num < 0) {
        printf("Factorial is not defined for negative numbers.\\n");
    } else {
        printf("Factorial of %d = %lld\\n", num, factorial(num));
    }

    return 0;
}

/*
Example Output:
Enter a number: 5
Factorial of 5 = 120

Explanation: 5! = 5 × 4 × 3 × 2 × 1 = 120
*/`}
        />
      </div>
    </div>
  );
}

// Patterns Section
function PatternsSection() {
  return (
    <div className="space-y-8">
      <div className="text-center space-y-4">
        <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white">
          Star Patterns
        </h2>
        <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
          Master nested loops and pattern logic with these visual programming
          exercises
        </p>
      </div>

      <div className="space-y-8">
        <CodeBlock
          title="1. Half Pyramid of star"
          description="Simple half pyramid pattern using nested loops"
          code={`#include <stdio.h>

int main() {
    int i, j, rows;

    printf("Enter the number of rows: ");
    scanf("%d", &rows);

    for (i = 1; i <= rows; ++i) {
        for (j = 1; j <= i; ++j) {
            printf("* ");
        }
        printf("\\n");
    }

    return 0;
}

/*
Example Output for rows = 5:
*
* *
* * *
* * * *
* * * * *
*/`}
        />

        <CodeBlock
          title="2. Half Pyramid of Numbers"
          description="Half pyramid pattern with incrementing numbers"
          code={`#include <stdio.h>

int main() {
    int i, j, rows;

    printf("Enter the number of rows: ");
    scanf("%d", &rows);

    for (i = 1; i <= rows; ++i) {
        for (j = 1; j <= i; ++j) {
            printf("%d ", j);
        }
        printf("\\n");
    }

    return 0;
}

/*
Example Output for rows = 5:
1
1 2
1 2 3
1 2 3 4
1 2 3 4 5
*/`}
        />

        <CodeBlock
          title="3. Half Pyramid of Alphabets"
          description="Half pyramid pattern with alphabets"
          code={`#include <stdio.h>

int main() {
    int i, j;
    char input, alphabet = 'A';

    printf("Enter an uppercase character you want to print in the last row: ");
    scanf("%c", &input);

    for (i = 1; i <= (input - 'A' + 1); ++i) {
        for (j = 1; j <= i; ++j) {
            printf("%c ", alphabet);
        }
        ++alphabet;
        printf("\\n");
    }

    return 0;
}

/*
Example Output for input = 'E':
A
B B
C C C
D D D D
E E E E E
*/`}
        />

        <CodeBlock
          title="4. Inverted Half Pyramid of star"
          description="Inverted half pyramid pattern with stars"
          code={`#include <stdio.h>

int main() {
    int i, j, rows;

    printf("Enter the number of rows: ");
    scanf("%d", &rows);

    for (i = rows; i >= 1; --i) {
        for (j = 1; j <= i; ++j) {
            printf("* ");
        }
        printf("\\n");
    }

    return 0;
}

/*
Example Output for rows = 5:
* * * * *
* * * *
* * *
* *
*
*/`}
        />

        <CodeBlock
          title="5. Inverted Half Pyramid of Numbers"
          description="Inverted half pyramid pattern with numbers"
          code={`#include <stdio.h>

int main() {
    int i, j, rows;

    printf("Enter the number of rows: ");
    scanf("%d", &rows);

    for (i = rows; i >= 1; --i) {
        for (j = 1; j <= i; ++j) {
            printf("%d ", j);
        }
        printf("\\n");
    }

    return 0;
}

/*
Example Output for rows = 5:
1 2 3 4 5
1 2 3 4
1 2 3
1 2
1
*/`}
        />

        <CodeBlock
          title="6. Full Pyramid of star"
          description="Full pyramid pattern with stars and spaces"
          code={`#include <stdio.h>

int main() {
    int i, space, rows, k = 0;

    printf("Enter the number of rows: ");
    scanf("%d", &rows);

    for (i = 1; i <= rows; ++i, k = 0) {
        for (space = 1; space <= rows - i; ++space) {
            printf("  ");
        }
        while (k != 2 * i - 1) {
            printf("* ");
            ++k;
        }
        printf("\\n");
    }

    return 0;
}

/*
Example Output for rows = 5:
        *
      * * *
    * * * * *
  * * * * * * *
* * * * * * * * *
*/`}
        />

        <CodeBlock
          title="7. Full Pyramid of Numbers"
          description="Full pyramid pattern with numbers"
          code={`#include <stdio.h>

int main() {
    int i, space, rows, k = 0, count = 0, count1 = 0;

    printf("Enter the number of rows: ");
    scanf("%d", &rows);

    for (i = 1; i <= rows; ++i) {
        for (space = 1; space <= rows - i; ++space) {
            printf("  ");
            ++count;
        }
        while (k != 2 * i - 1) {
            if (count <= rows - 1) {
                printf("%d ", i + k);
                ++count;
            } else {
                ++count1;
                printf("%d ", (i + k - 2 * count1));
            }
            ++k;
        }
        count1 = count = k = 0;
        printf("\\n");
    }

    return 0;
}

/*
Example Output for rows = 5:
        1
      2 3 2
    3 4 5 4 3
  4 5 6 7 6 5 4
5 6 7 8 9 8 7 6 5
*/`}
        />

        <CodeBlock
          title="8. Inverted Full Pyramid of star"
          description="Inverted full pyramid pattern with stars"
          code={`#include <stdio.h>

int main() {
    int rows, i, j, space;

    printf("Enter the number of rows: ");
    scanf("%d", &rows);

    for (i = rows; i >= 1; --i) {
        for (space = 0; space < rows - i; ++space)
            printf("  ");
        for (j = i; j <= 2 * i - 1; ++j)
            printf("* ");
        for (j = 0; j < i - 1; ++j)
            printf("* ");
        printf("\\n");
    }

    return 0;
}

/*
Example Output for rows = 5:
* * * * * * * * *
  * * * * * * *
    * * * * *
      * * *
        *
*/`}
        />

        <CodeBlock
          title="9. Pascal's Triangle"
          description="Pascal's triangle pattern with binomial coefficients"
          code={`#include <stdio.h>

int main() {
    int rows, coef = 1, space, i, j;

    printf("Enter the number of rows: ");
    scanf("%d", &rows);

    for (i = 0; i < rows; i++) {
        for (space = 1; space <= rows - i; space++)
            printf("  ");
        for (j = 0; j <= i; j++) {
            if (j == 0 || i == 0)
                coef = 1;
            else
                coef = coef * (i - j + 1) / j;
            printf("%4d", coef);
        }
        printf("\\n");
    }

    return 0;
}

/*
Example Output for rows = 6:
           1
         1   1
       1   2   1
     1   3   3   1
   1   4   6   4   1
 1   5  10  10   5   1
*/`}
        />

        <CodeBlock
          title="10. Floyd's Triangle"
          description="Floyd's triangle pattern with consecutive numbers"
          code={`#include <stdio.h>

int main() {
    int rows, i, j, number = 1;

    printf("Enter the number of rows: ");
    scanf("%d", &rows);

    for (i = 1; i <= rows; i++) {
        for (j = 1; j <= i; ++j) {
            printf("%d ", number);
            ++number;
        }
        printf("\\n");
    }

    return 0;
}

/*
Example Output for rows = 4:
1
2 3
4 5 6
7 8 9 10
*/`}
        />
      </div>
    </div>
  );
}

// Basic Commands Section
function BasicCommandsSection() {
  return (
    <div className="space-y-8">
      <div className="text-center space-y-4">
        <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white">
          C Programming Basic Commands
        </h2>
        <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
          Essential C programming commands and syntax you must know to get
          started
        </p>
      </div>

      <div className="space-y-8">
        {/* Header Files */}
        <CodeBlock
          title="1. Essential Header Files"
          description="Common header files you'll need in C programming"
          code={`#include <stdio.h>      // Standard input/output functions
#include <stdlib.h>     // Standard library functions
#include <string.h>     // String manipulation functions
#include <math.h>       // Mathematical functions
#include <ctype.h>      // Character type functions`}
        />

        {/* Basic Structure */}
        <CodeBlock
          title="2. Basic Program Structure"
          description="The fundamental structure of a C program"
          code={`#include <stdio.h>

int main() {
    // Your code goes here
    printf("Hello, World!\\n");
    return 0;  // Indicates successful program execution
}`}
        />

        {/* Variables */}
        <CodeBlock
          title="3. Variables and Data Types"
          description="Different data types and how to declare variables"
          code={`int age = 25;                    // Integer (4 bytes)
float height = 5.9;              // Floating point (4 bytes)
double weight = 70.5;            // Double precision (8 bytes)
char grade = 'A';                // Single character (1 byte)
char name[50] = "John Doe";      // Character array (string)`}
        />

        {/* Constants */}
        <CodeBlock
          title="4. Constants"
          description="How to define constants in C"
          code={`const int MAX_SIZE = 100;        // Constant integer
#define PI 3.14159               // Preprocessor constant
#define MAX_STUDENTS 50          // Another preprocessor constant`}
        />

        {/* Input/Output */}
        <CodeBlock
          title="5. Input and Output"
          description="Basic input and output operations"
          code={`#include <stdio.h>

int main() {
    int number;
    char name[50];

    // Output
    printf("Hello, World!\\n");
    printf("Number: %d\\n", 42);
    printf("Float: %.2f\\n", 3.14);

    // Input
    printf("Enter your age: ");
    scanf("%d", &number);

    printf("Enter your name: ");
    scanf("%s", name);

    printf("Hello %s, you are %d years old\\n", name, number);

    return 0;
}`}
        />

        {/* If-Else */}
        <CodeBlock
          title="6. If-Else Statements"
          description="Conditional statements for decision making"
          code={`#include <stdio.h>

int main() {
    int num = 10;

    if (num > 0) {
        printf("Positive number\\n");
    } else if (num < 0) {
        printf("Negative number\\n");
    } else {
        printf("Zero\\n");
    }

    return 0;
}`}
        />

        {/* Switch */}
        <CodeBlock
          title="7. Switch Statement"
          description="Multiple choice selection using switch"
          code={`#include <stdio.h>

int main() {
    char grade = 'B';

    switch (grade) {
        case 'A':
            printf("Excellent!\\n");
            break;
        case 'B':
            printf("Good!\\n");
            break;
        case 'C':
            printf("Average\\n");
            break;
        default:
            printf("Invalid grade\\n");
    }

    return 0;
}`}
        />

        {/* For Loop */}
        <CodeBlock
          title="8. For Loop"
          description="Repeating code with for loops"
          code={`#include <stdio.h>

int main() {
    // Print numbers 1 to 5
    for (int i = 1; i <= 5; i++) {
        printf("%d ", i);
    }
    printf("\\n");

    return 0;
}`}
        />

        {/* While Loop */}
        <CodeBlock
          title="9. While Loop"
          description="Repeating code with while loops"
          code={`#include <stdio.h>

int main() {
    int i = 1;

    while (i <= 5) {
        printf("%d ", i);
        i++;
    }
    printf("\\n");

    return 0;
}`}
        />

        {/* Arrays */}
        <CodeBlock
          title="10. Arrays"
          description="Working with arrays in C"
          code={`#include <stdio.h>

int main() {
    // Array declaration and initialization
    int numbers[5] = {1, 2, 3, 4, 5};

    // Accessing array elements
    printf("First number: %d\\n", numbers[0]);
    printf("Last number: %d\\n", numbers[4]);

    // Loop through array
    for (int i = 0; i < 5; i++) {
        printf("%d ", numbers[i]);
    }
    printf("\\n");

    return 0;
}`}
        />

        {/* Strings */}
        <CodeBlock
          title="11. Strings"
          description="Working with strings and string functions"
          code={`#include <stdio.h>
#include <string.h>

int main() {
    char str1[50] = "Hello";
    char str2[50] = "World";
    char str3[100];

    // String length
    printf("Length: %lu\\n", strlen(str1));

    // String copy
    strcpy(str3, str1);

    // String concatenation
    strcat(str3, " ");
    strcat(str3, str2);
    printf("Result: %s\\n", str3);

    return 0;
}`}
        />

        {/* Functions */}
        <CodeBlock
          title="12. Functions"
          description="Creating and using functions"
          code={`#include <stdio.h>

// Function declaration
int add(int a, int b);

int main() {
    int result = add(5, 3);
    printf("5 + 3 = %d\\n", result);
    return 0;
}

// Function definition
int add(int a, int b) {
    return a + b;
}`}
        />
      </div>
    </div>
  );
}
