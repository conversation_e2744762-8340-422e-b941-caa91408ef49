// Tutorial Management System Data
export const tutorialData = {
  normalization: [
    {
      title: "📋  The Challenge - Try It Yourself!",
      description: `Here's an unnormalized tutorial management table. Can you figure out how to normalize it?
  
  **Original Messy Table:**
   UnitID | UnitName | TutorID | TutorName | TutorEmail | StudentID | StudentName | StudentEmail | EnrollDate
  
  
  **Problems to Identify:**
  • What information is being repeated?
  • How many separate tables should this become?
  • What would be the primary keys for each table?
  
  **Your Challenge:** Think about how you would split this into clean, organized tables before looking at the solution!`,
      language: "text",
    },
    {
      title: "💡 UI-Based Solution (phpMyAdmin Method)",
      description: `**Perfect! Here's how to normalize the tutorial management database using phpMyAdmin:**
  
  **Step 1: Open phpMyAdmin**
  1. Start XAMPP Control Panel
  2. Click "Start" for Apache and MySQL
  3. Click "Admin" next to MySQL to open phpMyAdmin
  4. Click "New" to create a new database
  5. Name it "tutorial_management" and click "Create"
  
  **Step 2: Create Tutors Table**
  1. Click on your database name "tutorial_management"
  2. Click "Create table"
  3. Table name: "tbl_tutors", Number of columns: 4
  4. Set up columns:
     - TutorID: VARCHAR(10), Primary Key
     - TutorName: VARCHAR(255), NOT NULL
     - TutorEmail: VARCHAR(255), UNIQUE
     - Department: VARCHAR(100)
  
  **Step 3: Create Units Table**
  1. Click "Create table" again
  2. Table name: "tbl_units", Number of columns: 4
  3. Set up columns:
     - UnitID: VARCHAR(10), Primary Key
     - UnitName: VARCHAR(255), NOT NULL
     - TutorID: VARCHAR(10), Index
     - Credits: INT
  
  **Step 4: Create Students Table**
  1. Click "Create table" again
  2. Table name: "tbl_students", Number of columns: 4
  3. Set up columns:
     - StudentID: VARCHAR(10), Primary Key
     - StudentName: VARCHAR(255), NOT NULL
     - StudentEmail: VARCHAR(255), UNIQUE
     - Phone: VARCHAR(15)
  
  **Step 5: Create Enrollment Table**
  1. Click "Create table" again
  2. Table name: "tbl_enrollment", Number of columns: 4
  3. Set up columns:
     - EnrollmentID: INT, Primary Key, Auto Increment
     - UnitID: VARCHAR(10), Index
     - StudentID: VARCHAR(10), Index
     - EnrollDate: DATE, NOT NULL
  
  **Step 6: Set Up Relationships**
  1. Go to tbl_units table → Set TutorID to reference tbl_tutors(TutorID)
  2. Go to tbl_enrollment table → Set UnitID to reference tbl_units(UnitID)
  3. Go to tbl_enrollment table → Set StudentID to reference tbl_students(StudentID)
  
  ✅ **Done! Your normalized database is ready!**`,
      code: ``,
      language: "text",
    },
    {
      title: "Normalization Guide - SQL Code Method",
      description: `**Complete SQL Code to Create Normalized Tutorial Management Database**
  
  Copy and paste this code into phpMyAdmin's SQL tab:`,
      code: `-- Step 1: Create Database
CREATE DATABASE IF NOT EXISTS tutorial_management;
USE tutorial_management;

-- Step 2: Create Tutors Table
-- TutorID is INT and AUTO_INCREMENT
CREATE TABLE tbl_tutors (
    TutorID INT PRIMARY KEY AUTO_INCREMENT,
    TutorName VARCHAR(255) NOT NULL,
    TutorEmail VARCHAR(255) UNIQUE,
    Department VARCHAR(100)
);

-- Step 3: Create Units Table
-- TutorID now matches tbl_tutors.TutorID (INT)
CREATE TABLE tbl_units (
    UnitID INT PRIMARY KEY AUTO_INCREMENT,
    UnitName VARCHAR(255) NOT NULL,
    TutorID INT, -- Changed from VARCHAR(10) to INT
    Credits INT,
    FOREIGN KEY (TutorID) REFERENCES tbl_tutors(TutorID)
);

-- Step 4: Create Students Table
-- StudentID is INT and AUTO_INCREMENT
CREATE TABLE tbl_students (
    StudentID INT PRIMARY KEY AUTO_INCREMENT,
    StudentName VARCHAR(255) NOT NULL,
    StudentEmail VARCHAR(255) UNIQUE,
    Phone VARCHAR(15)
);

-- Step 5: Create Enrollment Table
-- UnitID and StudentID now match their respective primary keys (INT)
CREATE TABLE tbl_enrollment (
    EnrollmentID INT PRIMARY KEY AUTO_INCREMENT,
    UnitID INT,    -- Changed from VARCHAR(10) to INT
    StudentID INT, -- Changed from VARCHAR(10) to INT
    EnrollDate DATE NOT NULL,
    FOREIGN KEY (UnitID) REFERENCES tbl_units(UnitID),
    FOREIGN KEY (StudentID) REFERENCES tbl_students(StudentID)
);

-- Step 6: Insert Sample Data
-- For AUTO_INCREMENT columns, omit the ID column in the INSERT statement
-- MySQL will automatically assign the next available integer ID.

-- Insert into tbl_tutors
INSERT INTO tbl_tutors (TutorName, TutorEmail, Department) VALUES
('Dr. Smith', '<EMAIL>', 'Computer Science'),
('Prof. Johnson', '<EMAIL>', 'Mathematics'),
('Dr. Brown', '<EMAIL>', 'Physics');

-- Insert into tbl_units
-- TutorID now takes integer values referencing the auto-generated IDs
INSERT INTO tbl_units (UnitName, TutorID, Credits) VALUES
('Database Design', 1, 3), -- Assuming TutorID 1 for Dr. Smith
('Web Development', 1, 4), -- Assuming TutorID 1 for Dr. Smith
('Calculus I', 2, 3);    -- Assuming TutorID 2 for Prof. Johnson

-- Insert into tbl_students
INSERT INTO tbl_students (StudentName, StudentEmail, Phone) VALUES
('Alice Wilson', '<EMAIL>', '555-0101'),
('Bob Davis', '<EMAIL>', '555-0102'),
('Carol Miller', '<EMAIL>', '555-0103');

-- Insert into tbl_enrollment
-- UnitID and StudentID now take integer values referencing the auto-generated IDs
INSERT INTO tbl_enrollment (UnitID, StudentID, EnrollDate) VALUES
(1, 1, '2024-01-15'), -- Assuming UnitID 1 for 'Database Design' and StudentID 1 for 'Alice Wilson'
(1, 2, '2024-01-16'), -- Assuming UnitID 1 for 'Database Design' and StudentID 2 for 'Bob Davis'
(2, 1, '2024-01-17'); -- Assuming UnitID 2 for 'Web Development' and StudentID 1 for 'Alice Wilson'
`,
      language: "sql",
    },
  ],
  relationships: [
    {
      title: "Understanding Tutorial Management Relationships",
      description: `**Why do we need relationships in our tutorial management system?**

  In a tutorial system, we have four main entities:
  - **Tutors** (who teach units)
  - **Units** (what courses are offered)
  - **Students** (who enroll in units)
  - **Enrollments** (tracking who enrolled in what and when)

  **The Relationships:**
  1. **Tutors → Units**: One tutor can teach multiple units (1-to-Many)
  2. **Units → Enrollments**: One unit can have multiple enrollments (1-to-Many)
  3. **Students → Enrollments**: One student can enroll in multiple units (1-to-Many)

  **Real-World Example:**
  - Dr. Smith (tutor) teaches "Database Design" and "Web Development" (units)
  - Alice and Bob (students) both enroll in "Database Design" (enrollments)
  - Alice also enrolls in "Web Development" (another enrollment)

  This way, we can easily track which students are enrolled in which units with which tutors!`,
      code: ``,
      language: "text",
    },
  ],
};
