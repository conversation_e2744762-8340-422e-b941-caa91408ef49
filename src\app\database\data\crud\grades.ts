// CRUD Operations Data - Student Grades System
export const crudData = {
  dbConnect: {
    title: "config.php - Database Connection",
    description: "This file handles the connection to your MySQL database",
    language: "php",
    code: `<?php
$servername = "localhost";
$username = "root"; // Default XAMPP username
$password = "";     // Default XAMPP password
$dbname = "student_grades";

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}
?>`,
  },
  indexPage: {
    title: "index.php - Homepage & Data Viewer",
    description:
      "Main interface with navigation links and comprehensive data display from all tables",
    language: "php",
    code: `<?php include 'config.php'; ?>
<!DOCTYPE html>
<html>
<head>
    <title>View Students</title>
</head>
<body>
    <h1>Student List</h1>
    <a href="create.php">Add New Student</a><br><br>
    
    <?php
    $sql = "SELECT * FROM tbl_students";
    $result = $conn->query($sql);

    if ($result->num_rows > 0) {
        echo "<table border='1'>";
        echo "<tr><th>ID</th><th>Name</th><th>Email</th><th>Phone</th><th>Actions</th></tr>";
        
        while($row = $result->fetch_assoc()) {
            echo "<tr>
                    <td>{$row['StudentID']}</td>
                    <td>{$row['StudentName']}</td>
                    <td>{$row['StudentEmail']}</td>
                    <td>{$row['StudentPhone']}</td>
                    <td>
                        <a href='update.php?id={$row['StudentID']}'>Edit</a> | 
                        <a href='delete.php?id={$row['StudentID']}'>Delete</a>
                    </td>
                </tr>";
        }
        echo "</table>";
    } else {
        echo "No students found";
    }
    $conn->close();
    ?>
</body>
</html>`,
  },
  createPage: {
    title: "create.php - Add New Records",
    description: "Forms for adding students, subjects, and grades",
    language: "php",
    code: `<?php include 'config.php'; ?>
<!DOCTYPE html>
<html>
<head>
    <title>Add Student</title>
</head>
<body>
    <h1>Add New Student</h1>
    <form method="post">
        Student ID: <input type="text" name="id" required><br>
        Name: <input type="text" name="name" required><br>
        Email: <input type="email" name="email" required><br>
        Phone: <input type="text" name="phone"><br>
        <input type="submit" value="Add Student">
    </form>

    <?php
    if ($_SERVER["REQUEST_METHOD"] == "POST") {
        $id = $_POST['id'];
        $name = $_POST['name'];
        $email = $_POST['email'];
        $phone = $_POST['phone'];
        
        $sql = "INSERT INTO tbl_students (StudentID, StudentName, StudentEmail, StudentPhone)
                VALUES ('$id', '$name', '$email', '$phone')";
        
        if ($conn->query($sql) === TRUE) {
            echo "New student added successfully!";
            header("Location: index.php");
        } else {
            echo "Error: " . $sql . "<br>" . $conn->error;
        }
        $conn->close();
    }
    ?>
    <br><a href="index.php">Back to List</a>
</body>
</html>`,
  },
  updatePage: {
    title: "update.php - Edit Records",
    description: "Forms for updating students, subjects, and grades",
    language: "php",
    code: `<?php include 'config.php'; ?>
<!DOCTYPE html>
<html>
<head>
    <title>Edit Student</title>
</head>
<body>
    <h1>Edit Student</h1>
    
    <?php
    $id = $_GET['id'];
    $sql = "SELECT * FROM tbl_students WHERE StudentID='$id'";
    $result = $conn->query($sql);
    $row = $result->fetch_assoc();
    ?>
    
    <form method="post">
        <input type="hidden" name="id" value="<?php echo $row['StudentID']; ?>">
        Name: <input type="text" name="name" value="<?php echo $row['StudentName']; ?>" required><br>
        Email: <input type="email" name="email" value="<?php echo $row['StudentEmail']; ?>" required><br>
        Phone: <input type="text" name="phone" value="<?php echo $row['StudentPhone']; ?>"><br>
        <input type="submit" value="Update">
    </form>

    <?php
    if ($_SERVER["REQUEST_METHOD"] == "POST") {
        $id = $_POST['id'];
        $name = $_POST['name'];
        $email = $_POST['email'];
        $phone = $_POST['phone'];
        
        $sql = "UPDATE tbl_students SET 
                StudentName='$name', 
                StudentEmail='$email', 
                StudentPhone='$phone' 
                WHERE StudentID='$id'";
        
        if ($conn->query($sql) === TRUE) {
            echo "Student updated successfully!";
            header("Location: index.php");
        } else {
            echo "Error updating record: " . $conn->error;
        }
        $conn->close();
    }
    ?>
    <br><a href="index.php">Back to List</a>
</body>
</html>`,
  },
  deletePage: {
    title: "delete.php - Remove Records",
    description: "Safe deletion with confirmation and dependency checking",
    language: "php",
    code: `<?php include 'config.php'; ?>
<!DOCTYPE html>
<html>
<head>
    <title>Delete Student</title>
</head>
<body>
    <h1>Delete Student</h1>
    
    <?php
    $id = $_GET['id'];
    
    // Try to delete
    $sql = "DELETE FROM tbl_students WHERE StudentID='$id'";
    
    if ($conn->query($sql) === TRUE) {
        echo "Student deleted successfully";
    } else {
        echo "Error deleting student: " . $conn->error . "<br>";
        echo "Note: Cannot delete students with existing grades (foreign key constraint)";
    }
    $conn->close();
    ?>
    <br><br>
    <a href="index.php">Back to Student List</a>
</body>
</html>`,
  },
};
