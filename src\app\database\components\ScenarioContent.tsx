"use client";

import { useState } from "react";
import { Database, Table, Code, FileCode } from "lucide-react";

import {
  libraryData,
  ecommerceData,
  gradesData,
  tutorialData,
  universityData,
  hospitalData,
} from "../data/normalization_relation";
import CodeBlock from "./CodeBlock";
import { TableScenario } from "../data/tableScenarios";
import { CrudSection } from "./CrudSection";

interface ScenarioContentProps {
  scenario: TableScenario;
  onBack: () => void;
}

export default function ScenarioContent({
  scenario,
  onBack,
}: ScenarioContentProps) {
  // For "Beginner crud" scenario, only show CRUD operations
  const isBeginnerCrud = scenario.id === "Beginner crud";

  // Set default active section based on scenario type
  const [activeSection, setActiveSection] = useState(
    isBeginnerCrud ? "crud" : "normalization"
  );

  // Filter sections based on scenario type
  const allSections = [
    { id: "normalization", label: "Normalization Guide", icon: Database },
    { id: "relationships", label: "Table Relationships", icon: Table },
    {
      id: "code",
      label: "Code for Normalization and Relations",
      icon: FileCode,
    },
    { id: "crud", label: "CRUD Operations", icon: Code },
  ];

  // For "Beginner crud", only show CRUD section
  const sections = isBeginnerCrud
    ? allSections.filter((section) => section.id === "crud")
    : allSections;

  return (
    <div className="space-y-6">
      {/* Back Button */}
      <div className="flex items-center">
        <button
          onClick={onBack}
          className="inline-flex items-center text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors"
        >
          <svg
            className="h-4 w-4 mr-2"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M15 19l-7-7 7-7"
            />
          </svg>
          Back to Scenarios
        </button>
      </div>

      {/* Header Section */}
      <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-6">
        <div className="flex items-start gap-4">
          <span className="text-3xl">{scenario.icon}</span>
          <div className="space-y-2">
            <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white">
              {scenario.title}
            </h2>
            <p className="text-base text-gray-600 dark:text-gray-400 max-w-2xl">
              {scenario.description}
            </p>
          </div>
        </div>

        {/* Difficulty Badge */}
        <div className="flex-shrink-0">
          <span
            className={`inline-flex items-center px-4 py-2 rounded-full text-sm font-medium ${
              scenario.difficultyColor === "green"
                ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                : scenario.difficultyColor === "yellow"
                ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"
                : "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
            }`}
          >
            {scenario.difficulty === "Beginner" && "🟢"}
            {scenario.difficulty === "Intermediate" && "🟡"}
            {scenario.difficulty === "Advanced" && "🔴"} {scenario.difficulty}
          </span>
        </div>
      </div>

      {/* Section Navigation */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="-mb-px flex space-x-8 overflow-x-auto">
          {sections.map((section) => {
            const Icon = section.icon;
            return (
              <button
                key={section.id}
                onClick={() => setActiveSection(section.id)}
                className={`flex items-center py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                  activeSection === section.id
                    ? "border-blue-500 text-blue-600 dark:text-blue-400"
                    : "border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 hover:border-gray-300"
                }`}
              >
                <Icon className="h-4 w-4 mr-2" />
                {section.label}
              </button>
            );
          })}
        </nav>
      </div>

      {/* Section Content */}
      <div className="mt-6">
        {activeSection === "normalization" && (
          <NormalizationSection scenario={scenario} />
        )}
        {activeSection === "relationships" && (
          <RelationshipsSection scenario={scenario} />
        )}
        {activeSection === "code" && <CodeSection scenario={scenario} />}
        {activeSection === "crud" && <CrudSection scenario={scenario} />}
      </div>
    </div>
  );
}

// Normalization Section Component
function NormalizationSection({ scenario }: { scenario: TableScenario }) {
  const [activeNormTab, setActiveNormTab] = useState(0);

  // Choose the appropriate data based on scenario
  const getNormalizationData = () => {
    switch (scenario.id) {
      case "library":
        return libraryData.normalization;
      case "grades":
        return gradesData.normalization;
      case "tutorial":
        return tutorialData.normalization;
      case "ecommerce":
        return ecommerceData.normalization;
      case "university":
        return universityData.normalization;
      case "hospital":
        return hospitalData.normalization;
      default:
        // This should never happen since all scenarios are properly defined
        return tutorialData.normalization;
    }
  };

  const data = getNormalizationData();

  // All scenarios now use the same 2-tab structure: Question and Solution
  const questionData = data[0]; // Tab 1: The Challenge
  const uiMethodData = data[1]; // UI Method (phpMyAdmin instructions)

  const tabs = ["📋 Question", "💡 Solution"];

  return (
    <div className="space-y-6">
      {/* Tab Navigation */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="-mb-px flex space-x-8 overflow-x-auto">
          {tabs.map((tab, index) => (
            <button
              key={index}
              onClick={() => setActiveNormTab(index)}
              className={`flex items-center py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                activeNormTab === index
                  ? "border-blue-500 text-blue-600 dark:text-blue-400"
                  : "border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 hover:border-gray-300"
              }`}
            >
              {tab}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="mt-6">
        {/* Question Tab */}
        {activeNormTab === 0 && (
          <div className="space-y-4">
            <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
              <h4 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">
                {questionData.title}
              </h4>
              <div className="prose dark:prose-invert max-w-none">
                <div
                  className="text-gray-700 dark:text-gray-300 whitespace-pre-line"
                  dangerouslySetInnerHTML={{
                    __html: questionData.description.replace(
                      /\*\*(.*?)\*\*/g,
                      "<strong>$1</strong>"
                    ),
                  }}
                />
              </div>
            </div>
          </div>
        )}

        {/* Solution Tab - Contains UI method */}
        {activeNormTab === 1 && (
          <div className="space-y-6">
            {/* UI Method Section */}
            <div className="space-y-6">
              <div className="border-l-4 border-blue-500 pl-6">
                <h3 className="text-2xl font-bold mb-4 text-blue-800 dark:text-blue-200">
                  🖱️ UI-Based Solution (phpMyAdmin)
                </h3>
                <div className="prose dark:prose-invert max-w-none">
                  <div
                    className="text-gray-700 dark:text-gray-300 whitespace-pre-line leading-relaxed text-base"
                    dangerouslySetInnerHTML={{
                      __html: uiMethodData.description.replace(
                        /\*\*(.*?)\*\*/g,
                        "<strong class='text-blue-600 dark:text-blue-400'>$1</strong>"
                      ),
                    }}
                  />
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

// Relationships Section Component
function RelationshipsSection({ scenario }: { scenario: TableScenario }) {
  // Choose the appropriate data based on scenario
  const getRelationshipData = () => {
    switch (scenario.id) {
      case "library":
        return libraryData.relationships;
      case "grades":
        return gradesData.relationships;
      case "tutorial":
        return tutorialData.relationships;
      case "ecommerce":
        return ecommerceData.relationships;
      case "university":
        return universityData.relationships;
      case "hospital":
        return hospitalData.relationships;
      default:
        // This should never happen since all scenarios are properly defined
        return tutorialData.relationships;
    }
  };

  const data = getRelationshipData();

  // All scenarios now use the same simplified structure
  const relationshipInfo = data[0]; // First item contains the relationship explanation

  return (
    <div className="space-y-6">
      <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
        Table Relationships
      </h3>
      <p className="text-gray-600 dark:text-gray-400">
        Create relationships between tables in your{" "}
        {scenario.title.toLowerCase()} database.
      </p>

      {/* Relationship Overview */}
      <div className="bg-blue-50 dark:bg-gray-700 border border-blue-200 dark:border-blue-600 p-6 rounded-lg">
        <h4 className="text-lg font-semibold mb-3 text-blue-800 dark:text-blue-200">
          🔗 {scenario.title} Relationships
        </h4>
        <div className="prose dark:prose-invert max-w-none">
          <div
            className="text-blue-700 dark:text-blue-300 whitespace-pre-line"
            dangerouslySetInnerHTML={{
              __html: relationshipInfo.description.replace(
                /\*\*(.*?)\*\*/g,
                "<strong>$1</strong>"
              ),
            }}
          />
        </div>
      </div>

      {/* phpMyAdmin Instructions */}
      <div className="bg-green-50 dark:bg-gray-700 border border-green-200 dark:border-green-600 p-6 rounded-lg">
        <h4 className="text-lg font-semibold mb-3 text-green-800 dark:text-green-200">
          🖱️ How to Create Relationships in phpMyAdmin
        </h4>
        <div className="space-y-3">
          <div>
            <h5 className="font-semibold text-green-800 dark:text-green-200 mb-2">
              Quick Method:
            </h5>
            <ol className="list-decimal list-inside space-y-1 text-green-700 dark:text-green-300 text-sm">
              <li>Open phpMyAdmin → Select your database</li>
              <li>
                Click <strong>&quot;Designer&quot;</strong> tab
              </li>
              <li>Drag between related fields to create relationships</li>
              <li>
                Alternatively, use <strong>&quot;Relation View&quot;</strong> in
                each table&apos;s Structure tab
              </li>
              <li>Done! Relationship lines will appear</li>
            </ol>
          </div>
        </div>
      </div>
    </div>
  );
}

// Code Section Component - SQL Code for Normalization and Relations
function CodeSection({ scenario }: { scenario: TableScenario }) {
  // Get normalization and relationship data
  const getNormalizationData = () => {
    switch (scenario.id) {
      case "library":
        return libraryData.normalization;
      case "grades":
        return gradesData.normalization;
      case "tutorial":
        return tutorialData.normalization;
      case "ecommerce":
        return ecommerceData.normalization;
      case "university":
        return universityData.normalization;
      case "hospital":
        return hospitalData.normalization;
      default:
        // This should never happen since all scenarios are properly defined
        return tutorialData.normalization;
    }
  };

  const normalizationCodeData = getNormalizationData();

  return (
    <div className="space-y-6">
      <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
        💻 SQL Code Reference
      </h3>
      <p className="text-gray-600 dark:text-gray-400 text-lg">
        Complete SQL code for database normalization and table relationships.
      </p>

      {/* Normalization SQL Code */}
      <div className="space-y-4">
        <CodeBlock
          title={`Complete ${scenario.title} Database SQL Code`}
          description="Copy and paste this code into phpMyAdmin's SQL tab to create the complete normalized database with relationships."
          code={normalizationCodeData[2]?.code || ""}
          language="sql"
        />
      </div>

      {/* Relationships Code */}
      <div className="space-y-4">
        <h4 className="text-xl font-semibold text-gray-900 dark:text-white">
          🔗 Table Relationships
        </h4>
        <CodeBlock
          title={`Foreign Key Relationships for ${scenario.title}`}
          description="SQL code to create relationships between tables. Note: These are already included in the normalization code above, but shown here separately for reference."
          code={`-- Create Foreign Key Relationships for ${scenario.title}

-- Note: If tables already exist with data, you may need to add constraints separately
-- The relationships below are already included in the CREATE TABLE statements above

-- Example: Adding foreign key constraints
-- ALTER TABLE child_table
-- ADD CONSTRAINT fk_constraint_name
-- FOREIGN KEY (foreign_key_column) REFERENCES parent_table(primary_key_column)
-- ON DELETE CASCADE
-- ON UPDATE CASCADE;

-- Verify relationships were created
-- SHOW CREATE TABLE your_table_name;

-- Check all foreign keys in the database
SELECT
    TABLE_NAME,
    COLUMN_NAME,
    CONSTRAINT_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM
    INFORMATION_SCHEMA.KEY_COLUMN_USAGE
WHERE
    REFERENCED_TABLE_SCHEMA = DATABASE()
    AND REFERENCED_TABLE_NAME IS NOT NULL;`}
          language="sql"
        />
      </div>
    </div>
  );
}
