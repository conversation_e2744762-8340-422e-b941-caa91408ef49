// CRUD Operations Data - Beginner Level (Users Table Only)
export const crudData = {
  dbSetup: {
    title: "Database Setup - Create Database and Table",
    description: "First, create the database and users table in phpMyAdmin",
    language: "sql",
    code: `-- Step 1: Create Database
CREATE DATABASE learn_db;

-- Step 2: Use the Database
USE learn_db;

-- Step 3: Create Users Table
CREATE TABLE users (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    email VARCHAR(100) NOT NULL UNIQUE,
    gender ENUM('male', 'female') NOT NULL,
    age INT(3) NOT NULL
);

-- Step 4: Insert Sample Data (Optional)
INSERT INTO users (first_name, last_name, email, gender, age) VALUES
('<PERSON>', '<PERSON><PERSON>', '<EMAIL>', 'male', 25),
('<PERSON>', '<PERSON>', '<EMAIL>', 'female', 30),
('<PERSON>', '<PERSON>', '<EMAIL>', 'male', 28);`,
  },
  dbConnect: {
    title: "config.php - Database Connection",
    description: "This file handles the connection to your MySQL database",
    language: "php",
    code: `<?php
$servername = 'localhost';
$username = 'root';
$password = '';
$dbname = 'learn_db';

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}
?>`,
  },
  indexPage: {
    title: "index.php - Homepage & Data Viewer",
    description:
      "Main interface with navigation links and comprehensive data display from all tables",
    language: "php",
    code: `<?php
include "config.php";

$sql = "SELECT * FROM \`users\`";

$result = $conn->query($sql);

?>
<!DOCTYPE html>
<html>
<head>
    <title>View Users</title>
    <link rel="stylesheet" href="crud-styles.css">
</head>
<body>
    <h1>All Users</h1>
    <a href="create.php">Add New User</a> <br><br>

    <table>
        <thead>
            <tr>
                <th>ID</th>
                <th>First Name</th>
                <th>Last Name</th>
                <th>Email</th>
                <th>Gender</th>
                <th>Age</th>
                <th>Action</th>
            </tr>
        </thead>
        <tbody>
            <?php
            // Check if there are any results
            if ($result->num_rows > 0) {
                // Loop through each row of data
                while ($row = $result->fetch_assoc()) {
                    ?>
                    <tr>
                        <td><?php echo $row['id']; ?></td>
                        <td><?php echo $row['first_name']; ?></td>
                        <td><?php echo $row['last_name']; ?></td>
                        <td><?php echo $row['email']; ?></td>
                        <td><?php echo $row['gender']; ?></td>
                        <td><?php echo $row['age']; ?></td>
                        <td>
                            <a href="update.php?id=<?php echo $row['id']; ?>">Edit</a> |
                            <a href="delete.php?id=<?php echo $row['id']; ?>" onclick="return confirm('Are you sure you want to delete this user?');">Delete</a>
                        </td>
                    </tr>
                    <?php
                }
            } else {
                echo "<tr><td colspan='7'>No users found.</td></tr>";
            }
            ?>
        </tbody>
    </table>

    <?php
    // Close the database connection
    $conn->close();
    ?>
</body>
</html>`,
  },
  createPage: {
    title: " create.php - Add New Records",
    description:
      "Complete forms for adding units, tutors, students, and enrollments",
    language: "php",
    code: `<?php
include "config.php";

// Check if the form has been submitted
if (isset($_POST["submit"])) {
    // Get data from the form
    $first_name = $_POST["first_name"];
    $last_name = $_POST["last_name"];
    $email = $_POST["email"];
    $gender = $_POST["gender"];
    $age = (int)$_POST["age"];

    $sql = "INSERT INTO \`users\` (\`first_name\`, \`last_name\`, \`email\`, \`gender\`, \`age\`) VALUES ('$first_name', '$last_name', '$email', '$gender', '$age')";

    // Execute the query
    $result = $conn->query($sql);

    // Check if the query was successful
    if ($result == true) {
        echo "New record created successfully!";
    } else {
        // Display an error message if the query fails
        echo "Error: " . $sql . "<br>" . $conn->error;
    }

    // Close the database connection after processing the form
    $conn->close();
}
?>
<!DOCTYPE html>
<html>
<head>
    <title>Create User</title>
    <link rel="stylesheet" href="crud-styles.css">
</head>
<body>
    <h1>Signup Form</h1>
    <form action="" method="POST">
        <fieldset>
            <legend>Personal information:</legend>
            First Name: <br>
            <input type="text" name="first_name">
            <br>
            Last Name: <br>
            <input type="text" name="last_name">
            <br>
            Email: <br>
            <input type="email" name="email">
            <br>
            Gender: <br>
            <input type="radio" name="gender" value="male">male
            <input type="radio" name="gender" value="female">female
            <br><br>
            Age: <br>
            <input type="number" name="age">
            <br>
            <br>
            <input type="submit" name="submit" value="Submit">
        </fieldset>
    </form>
</body>
</html>`,
  },

  updatePage: {
    title: " update.php - Edit Records",
    description: "Forms for updating existing records with validation",
    language: "php",
    code: `<?php
include "config.php";

// Get the user ID from the URL
$id = $_GET['id'];

// Check if the form has been submitted for update
if (isset($_POST["update"])) {
    // Get data from the form
    $first_name = $_POST["first_name"];
    $last_name = $_POST["last_name"];
    $email = $_POST["email"];
    $gender = $_POST["gender"];
    $age = (int)$_POST["age"];

    // SQL query to update the record
    $sql = "UPDATE \`users\` SET \`first_name\`='$first_name', \`last_name\`='$last_name', \`email\`='$email', \`gender\`='$gender', \`age\`='$age' WHERE \`id\`='$id'";

    // Execute the query
    $result = $conn->query($sql);

    // Check if the query was successful
    if ($result == true) {
        echo "Record updated successfully!";
        // Optional: Redirect back to the read page after update
        header("Location: index.php");
        exit();
    } else {
        // Display an error message if the query fails
        echo "Error: " . $sql . "<br>" . $conn->error;
    }
}

// Fetch the current user data to populate the form
$sql = "SELECT * FROM \`users\` WHERE \`id\`='$id'";
$result = $conn->query($sql);
$user = $result->fetch_assoc();
?>
<!DOCTYPE html>
<html>
<head>
    <title>Update User</title>
    <link rel="stylesheet" href="crud-styles.css">
</head>
<body>
    <h1>Update User Information</h1>
    <form action="" method="POST">
        <fieldset>
            <legend>Personal information:</legend>
            First Name: <br>
            <input type="text" name="first_name" value="<?php echo $user['first_name']; ?>" required>
            <br>
            Last Name: <br>
            <input type="text" name="last_name" value="<?php echo $user['last_name']; ?>" required>
            <br>
            Email: <br>
            <input type="email" name="email" value="<?php echo $user['email']; ?>" required>
            <br>
            Gender: <br>
            <input type="radio" name="gender" value="male" <?php echo ($user['gender'] == 'male') ? 'checked' : ''; ?>>male
            <input type="radio" name="gender" value="female" <?php echo ($user['gender'] == 'female') ? 'checked' : ''; ?>>female
            <br><br>
            Age: <br>
            <input type="number" name="age" value="<?php echo $user['age']; ?>" required>
            <br>
            <br>
            <input type="submit" name="update" value="Update">
            <a href="index.php">Cancel</a>
        </fieldset>
    </form>
</body>
</html>`,
  },
  deletePage: {
    title: " delete.php - Remove Records",
    description: "Safe deletion with confirmation and foreign key handling",
    language: "php",
    code: `<?php
include "config.php"; // Include your database connection file

// Check if the 'id' parameter is set in the URL (GET request)
if (isset($_GET["id"])) {
    $id = $_GET["id"]; // Get the user ID from the URL

    // SQL query to delete the record
    $sql = "DELETE FROM \`users\` WHERE \`id\`='$id'";

    $result = $conn->query($sql);

    if ($result == true) {
        echo "Record deleted successfully!";
        // Optional: Redirect back to the read page after deletion
        header("Location: index.php");
        exit(); // Always exit after header redirect
    } else {
        echo "Error: " . $sql . "<br>" . $conn->error;
    }
} else {
    echo "No ID provided for deletion.";
}

// Close the database connection
$conn->close();
?>
<!DOCTYPE html>
<html>
<head>
    <title>Delete User</title>
    <link rel="stylesheet" href="crud-styles.css">
</head>
<body>
    <br>
    <a href="index.php">Back to All Users</a>
</body>
</html>`,
  },
};
