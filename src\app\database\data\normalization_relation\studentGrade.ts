// Student Grades System Data
export const gradesData = {
  normalization: [
    {
      title: "📋  The Challenge - Try It Yourself!",
      description: `Here's an unnormalized student grades table. Can you figure out how to normalize it?
  
  **Original Messy Table:**
   StudentID | StudentName | StudentEmail | SubjectID | SubjectName | Credits | Grade | Semester
  
  
  **Problems to Identify:**
  • What information is being repeated?
  • How many separate tables should this become?
  • What would be the primary keys for each table?
  
  **Your Challenge:** Think about how you would split this into clean, organized tables before looking at the solution!`,
      language: "text",
    },
    {
      title: "💡 UI-Based Solution (phpMyAdmin Method)",
      description: `**Perfect! Here's how to normalize the student grades database using phpMyAdmin:**
  
  **Step 1: Open phpMyAdmin**
  1. Start XAMPP Control Panel
  2. Click "Start" for Apache and MySQL
  3. Click "Admin" next to MySQL to open phpMyAdmin
  4. Click "New" to create a new database
  5. Name it "student_grades" and click "Create"
  
  **Step 2: Create Students Table**
  1. Click on your database name "student_grades"
  2. Click "Create table"
  3. Table name: "tbl_students", Number of columns: 4
  4. Set up columns:
     - StudentID: VARCHAR(10), Primary Key
     - StudentName: VARCHAR(255), NOT NULL
     - StudentEmail: VARCHAR(255), UNIQUE
     - Phone: VARCHAR(15)
  
  **Step 3: Create Subjects Table**
  1. Click "Create table" again
  2. Table name: "tbl_subjects", Number of columns: 3
  3. Set up columns:
     - SubjectID: VARCHAR(10), Primary Key
     - SubjectName: VARCHAR(255), NOT NULL
     - Credits: INT, NOT NULL
  
  **Step 4: Create Grades Table**
  1. Click "Create table" again
  2. Table name: "tbl_grades", Number of columns: 5
  3. Set up columns:
     - GradeID: INT, Primary Key, Auto Increment
     - StudentID: VARCHAR(10), Index
     - SubjectID: VARCHAR(10), Index
     - Grade: VARCHAR(5), NOT NULL
     - Semester: VARCHAR(20), NOT NULL
  
  **Step 5: Set Up Relationships**
  1. Go to tbl_grades table
  2. Click "Structure" tab
  3. Click "Relation View"
  4. Set StudentID to reference tbl_students(StudentID)
  5. Set SubjectID to reference tbl_subjects(SubjectID)
  
  ✅ **Done! Your normalized database is ready!**`,
      code: ``,
      language: "text",
    },
    {
      title: "Normalization Guide - SQL Code Method",
      description: `**Complete SQL Code to Create Normalized Student Grades Database**
  
  Copy and paste this code into phpMyAdmin's SQL tab:`,
      code: `-- Step 1: Create Database for Student Grades
-- Using IF NOT EXISTS to avoid error if it already exists
CREATE DATABASE IF NOT EXISTS student_grades;
USE student_grades;

-- Step 2: Create Students Table
CREATE TABLE IF NOT EXISTS tbl_students (
    StudentID VARCHAR(10) PRIMARY KEY, -- Using VARCHAR for StudentID as in your data
    StudentName VARCHAR(255) NOT NULL,
    StudentEmail VARCHAR(255) UNIQUE,
    StudentPhone VARCHAR(20)
);

-- Step 3: Create Subjects Table
CREATE TABLE IF NOT EXISTS tbl_subjects (
    SubjectID VARCHAR(10) PRIMARY KEY, -- Using VARCHAR for SubjectID as in your data
    SubjectName VARCHAR(255) NOT NULL,
    Credits INT NOT NULL
);

-- Step 4: Create Grades Table
-- This table links students to subjects with their grades
CREATE TABLE IF NOT EXISTS tbl_grades (
    GradeID INT PRIMARY KEY AUTO_INCREMENT,
    StudentID VARCHAR(10) NOT NULL,
    SubjectID VARCHAR(10) NOT NULL,
    Grade VARCHAR(5) NOT NULL, -- e.g., 'A', 'B+', 'C-'
    Semester VARCHAR(50) NOT NULL,
    -- Ensure no duplicate grade entries for the same student in the same subject and semester
    UNIQUE (StudentID, SubjectID, Semester),
    -- Foreign Key to tbl_students:
    -- ON DELETE RESTRICT prevents deleting a student if they have existing grades
    -- ON UPDATE CASCADE updates StudentID in tbl_grades if StudentID changes in tbl_students
    FOREIGN KEY (StudentID) REFERENCES tbl_students(StudentID) ON DELETE RESTRICT ON UPDATE CASCADE,
    -- Foreign Key to tbl_subjects:
    -- ON DELETE RESTRICT prevents deleting a subject if it has existing grades
    -- ON UPDATE CASCADE updates SubjectID in tbl_grades if SubjectID changes in tbl_subjects
    FOREIGN KEY (SubjectID) REFERENCES tbl_subjects(SubjectID) ON DELETE RESTRICT ON UPDATE CASCADE
);

-- Step 5: Insert Sample Data into Students Table (Parent Table)
INSERT INTO tbl_students (StudentID, StudentName, StudentEmail, StudentPhone) VALUES
('S001', 'Alice Johnson', '<EMAIL>', '555-0101'),
('S002', 'Bob Smith', '<EMAIL>', '555-0102'),
('S003', 'Carol Davis', '<EMAIL>', '555-0103');

-- Step 6: Insert Sample Data into Subjects Table (Parent Table)
INSERT INTO tbl_subjects (SubjectID, SubjectName, Credits) VALUES
('SUB001', 'Database Systems', 3),
('SUB002', 'Web Development', 4),
('SUB003', 'Data Structures', 3);

-- Step 7: Insert Sample Data into Grades Table (Child Table)
-- This will now work because 'S001', 'S002', 'SUB001', and 'SUB002' exist in their respective parent tables
INSERT INTO tbl_grades (GradeID, StudentID, SubjectID, Grade, Semester) VALUES
(1, 'S001', 'SUB001', 'A', 'Fall 2024'),
(2, 'S001', 'SUB002', 'B+', 'Fall 2024'),
(3, 'S002', 'SUB001', 'A-', 'Fall 2024');`,
      language: "sql",
    },
  ],
  relationships: [
    {
      title: "Understanding Student Grades Relationships",
      description: `**Why do we need relationships in our student grades system?**
  
  In an academic system, we have three main entities:
  - **Students** (who take courses)
  - **Subjects** (what courses are available)
  - **Grades** (tracking student performance in subjects)
  
  **The Relationships:**
  1. **Students → Grades**: One student can have multiple grades (1-to-Many)
  2. **Subjects → Grades**: One subject can have multiple student grades (1-to-Many)
  
  This way, we can easily track student performance across different subjects!`,
      code: ``,
      language: "text",
    },
  ],
};
