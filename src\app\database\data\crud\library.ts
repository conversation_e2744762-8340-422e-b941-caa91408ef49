// CRUD Operations Data - Library Management System
export const crudData = {
  dbConnect: {
    title: "config.php - Database Connection",
    description: "This file handles the connection to your MySQL database",
    language: "php",
    code: `<?php
$servername = "localhost";
$username = "root";
$password = "";
$dbname = "library_management";

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}
?>`,
  },
  indexPage: {
    title: "index.php - Homepage & Data Viewer",
    description:
      "Main interface with navigation links and comprehensive data display from all tables",
    language: "php",
    code: `<?php include "config.php"; ?>
<!DOCTYPE html>
<html>
<head>
    <title>Library System</title>
    <link rel="stylesheet" href="library-styles.css">
</head>
<body>
    <div class="container">
        <h1>📚 Library Management</h1>
        
        <div class="nav-buttons">
            <a href="create.php" class="btn">➕ Add Records</a>
            <a href="update.php" class="btn">✏️ Update Records</a>
            <a href="delete.php" class="btn">🗑️ Delete Records</a>
        </div>

        <!-- Books -->
        <h2>📖 Books</h2>
        <table>
            <tr>
                <th>Title</th>
                <th>Author</th>
                <th>ISBN</th>
            </tr>
            <?php
            $books = $conn->query("SELECT * FROM tbl_books");
            while($book = $books->fetch_assoc()):
            ?>
            <tr>
                <td><?= $book['Title'] ?></td>
                <td><?= $book['Author'] ?></td>
                <td><?= $book['ISBN'] ?></td>
            </tr>
            <?php endwhile; ?>
        </table>

        <!-- Members -->
        <h2>👤 Members</h2>
        <table>
            <tr>
                <th>Name</th>
                <th>Email</th>
            </tr>
            <?php
            $members = $conn->query("SELECT * FROM tbl_members");
            while($member = $members->fetch_assoc()):
            ?>
            <tr>
                <td><?= $member['MemberName'] ?></td>
                <td><?= $member['MemberEmail'] ?></td>
            </tr>
            <?php endwhile; ?>
        </table>

        <!-- Borrowings -->
        <h2>📋 Borrowings</h2>
        <table>
            <tr>
                <th>Book</th>
                <th>Member</th>
                <th>Status</th>
            </tr>
            <?php
            $borrowings = $conn->query("
                SELECT b.Title, m.MemberName, br.Status 
                FROM tbl_borrowing br
                JOIN tbl_books b ON br.BookID = b.BookID
                JOIN tbl_members m ON br.MemberID = m.MemberID
            ");
            while($borrow = $borrowings->fetch_assoc()):
            ?>
            <tr>
                <td><?= $borrow['Title'] ?></td>
                <td><?= $borrow['MemberName'] ?></td>
                <td><?= $borrow['Status'] ?></td>
            </tr>
            <?php endwhile; ?>
        </table>
    </div>
</body>
</html>`,
  },
  createPage: {
    title: "create.php - Add New Records",
    description:
      "Simple forms for adding books, members, and borrowing records",
    language: "php",
    code: `<?php include "config.php"; ?>
<!DOCTYPE html>
<html>
<head>
    <title>Add Records</title>
    <link rel="stylesheet" href="library-styles.css">
</head>
<body>
    <div class="container">
        <h1>➕ Add Records</h1>
        <a href="index.php" class="btn">← Back to Main</a>

        <!-- Add Book -->
        <h2>📖 Add Book</h2>
        <form method="POST" action="create.php">
            <input type="text" name="title" placeholder="Title" required>
            <input type="text" name="author" placeholder="Author" required>
            <input type="text" name="isbn" placeholder="ISBN" required>
            <button type="submit" name="add_book" class="btn">Add Book</button>
        </form>

        <!-- Add Member -->
        <h2>👤 Add Member</h2>
        <form method="POST" action="create.php">
            <input type="text" name="name" placeholder="Name" required>
            <input type="email" name="email" placeholder="Email" required>
            <button type="submit" name="add_member" class="btn">Add Member</button>
        </form>

        <!-- Borrow Book -->
        <h2>📋 Borrow Book</h2>
        <form method="POST" action="create.php">
            <select name="book_id" required>
                <option value="">Select Book</option>
                <?php
                $books = $conn->query("SELECT * FROM tbl_books");
                while($book = $books->fetch_assoc()):
                ?>
                <option value="<?= $book['BookID'] ?>">
                    <?= $book['Title'] ?>
                </option>
                <?php endwhile; ?>
            </select>

            <select name="member_id" required>
                <option value="">Select Member</option>
                <?php
                $members = $conn->query("SELECT * FROM tbl_members");
                while($member = $members->fetch_assoc()):
                ?>
                <option value="<?= $member['MemberID'] ?>">
                    <?= $member['MemberName'] ?>
                </option>
                <?php endwhile; ?>
            </select>

            <button type="submit" name="borrow_book" class="btn">Borrow Book</button>
        </form>

        <?php
        // Handle form submissions
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Add Book
            if (isset($_POST['add_book'])) {
                $title = $_POST['title'];
                $author = $_POST['author'];
                $isbn = $_POST['isbn'];
                
                $conn->query("INSERT INTO tbl_books (Title, Author, ISBN) 
                              VALUES ('$title', '$author', '$isbn')");
                header("Location: create.php");
            }
            
            // Add Member
            if (isset($_POST['add_member'])) {
                $name = $_POST['name'];
                $email = $_POST['email'];
                
                $conn->query("INSERT INTO tbl_members (MemberName, MemberEmail) 
                              VALUES ('$name', '$email')");
                header("Location: create.php");
            }
            
            // Borrow Book
            if (isset($_POST['borrow_book'])) {
                $book_id = $_POST['book_id'];
                $member_id = $_POST['member_id'];
                
                $conn->query("INSERT INTO tbl_borrowing (BookID, MemberID, BorrowDate, Status) 
                              VALUES ('$book_id', '$member_id', CURDATE(), 'Borrowed')");
                header("Location: create.php");
            }
        }
        ?>
    </div>
</body>
</html>`,
  },
  updatePage: {
    title: "update.php - Edit Records",
    description:
      "Simple forms for updating books, members, and returning books",
    language: "php",
    code: `<?php include "config.php"; ?>
<!DOCTYPE html>
<html>
<head>
    <title>Update Records</title>
    <link rel="stylesheet" href="library-styles.css">
</head>
<body>
    <div class="container">
        <h1>✏️ Update Records</h1>
        <a href="index.php" class="btn">← Back to Main</a>

        <!-- Update Books -->
        <h2>📖 Books</h2>
        <?php $books = $conn->query("SELECT * FROM tbl_books"); ?>
        <?php while($book = $books->fetch_assoc()): ?>
        <form method="POST" class="record-form">
            <input type="hidden" name="book_id" value="<?= $book['BookID'] ?>">
            
            <input type="text" name="title" value="<?= $book['Title'] ?>" placeholder="Title" required>
            <input type="text" name="author" value="<?= $book['Author'] ?>" placeholder="Author" required>
            <input type="text" name="isbn" value="<?= $book['ISBN'] ?>" placeholder="ISBN" required>
            
            <button type="submit" name="update_book" class="btn">Update</button>
        </form>
        <?php endwhile; ?>

        <!-- Update Members -->
        <h2>👤 Members</h2>
        <?php $members = $conn->query("SELECT * FROM tbl_members"); ?>
        <?php while($member = $members->fetch_assoc()): ?>
        <form method="POST" class="record-form">
            <input type="hidden" name="member_id" value="<?= $member['MemberID'] ?>">
            
            <input type="text" name="name" value="<?= $member['MemberName'] ?>" placeholder="Name" required>
            <input type="email" name="email" value="<?= $member['MemberEmail'] ?>" placeholder="Email" required>
            
            <button type="submit" name="update_member" class="btn">Update</button>
        </form>
        <?php endwhile; ?>

        <!-- Return Books -->
        <h2>📋 Return Books</h2>
        <?php
        $borrowings = $conn->query("
            SELECT br.BorrowID, b.Title, m.MemberName 
            FROM tbl_borrowing br
            JOIN tbl_books b ON br.BookID = b.BookID
            JOIN tbl_members m ON br.MemberID = m.MemberID
            WHERE br.Status = 'Borrowed'
        ");
        ?>
        <?php while($borrow = $borrowings->fetch_assoc()): ?>
        <form method="POST" class="record-form">
            <input type="hidden" name="borrow_id" value="<?= $borrow['BorrowID'] ?>">
            
            <p><?= $borrow['MemberName'] ?> → <?= $borrow['Title'] ?></p>
            
            <button type="submit" name="return_book" class="btn">Mark Returned</button>
        </form>
        <?php endwhile; ?>

        <?php
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Update Book
            if (isset($_POST['update_book'])) {
                $book_id = $_POST['book_id'];
                $title = $_POST['title'];
                $author = $_POST['author'];
                $isbn = $_POST['isbn'];
                
                $conn->query("UPDATE tbl_books SET 
                    Title = '$title', 
                    Author = '$author', 
                    ISBN = '$isbn' 
                    WHERE BookID = '$book_id'
                ");
                header("Location: update.php");
            }
            
            // Update Member
            if (isset($_POST['update_member'])) {
                $member_id = $_POST['member_id'];
                $name = $_POST['name'];
                $email = $_POST['email'];
                
                $conn->query("UPDATE tbl_members SET 
                    MemberName = '$name', 
                    MemberEmail = '$email' 
                    WHERE MemberID = '$member_id'
                ");
                header("Location: update.php");
            }
            
            // Return Book
            if (isset($_POST['return_book'])) {
                $borrow_id = $_POST['borrow_id'];
                
                $conn->query("UPDATE tbl_borrowing SET 
                    ReturnDate = CURDATE(),
                    Status = 'Returned' 
                    WHERE BorrowID = '$borrow_id'
                ");
                header("Location: update.php");
            }
        }
        ?>
    </div>
</body>
</html>`,
  },
  deletePage: {
    title: "delete.php - Remove Records",
    description: "Simple deletion with safety checks and confirmation",
    language: "php",
    code: `<?php include "config.php"; ?>
<!DOCTYPE html>
<html>
<head>
    <title>Delete Records</title>
    <link rel="stylesheet" href="library-styles.css">
</head>
<body>
    <div class="container">
        <h1>🗑️ Delete Records</h1>
        <a href="index.php" class="btn">← Back to Main</a>

        <!-- Delete Books -->
        <h2>📖 Books</h2>
        <?php $books = $conn->query("SELECT * FROM tbl_books"); ?>
        <?php while($book = $books->fetch_assoc()): ?>
        <div class="record-item">
            <form method="POST" onsubmit="return confirm('Delete this book?');">
                <input type="hidden" name="book_id" value="<?= $book['BookID'] ?>">
                
                <p>
                    <strong><?= $book['Title'] ?></strong> by <?= $book['Author'] ?>
                </p>
                
                <button type="submit" name="delete_book" class="delete-btn">Delete</button>
            </form>
        </div>
        <?php endwhile; ?>

        <!-- Delete Members -->
        <h2>👤 Members</h2>
        <?php $members = $conn->query("SELECT * FROM tbl_members"); ?>
        <?php while($member = $members->fetch_assoc()): ?>
        <div class="record-item">
            <form method="POST" onsubmit="return confirm('Delete this member?');">
                <input type="hidden" name="member_id" value="<?= $member['MemberID'] ?>">
                
                <p><strong><?= $member['MemberName'] ?></strong> (<?= $member['MemberEmail'] ?>)</p>
                
                <button type="submit" name="delete_member" class="delete-btn">Delete</button>
            </form>
        </div>
        <?php endwhile; ?>

        <?php
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            if (isset($_POST['delete_book'])) {
                $book_id = $_POST['book_id'];
                $conn->query("DELETE FROM tbl_books WHERE BookID = '$book_id'");
                header("Location: delete.php");
            }
            
            if (isset($_POST['delete_member'])) {
                $member_id = $_POST['member_id'];
                $conn->query("DELETE FROM tbl_members WHERE MemberID = '$member_id'");
                header("Location: delete.php");
            }
        }
        ?>
    </div>
</body>
</html>`,
  },
};
