"use client";

import { useState } from "react";
import {
  Network,
  Router,
  Globe,
  Layers,
  Copy,
  CheckCircle,
  Wifi,
} from "lucide-react";
import { Prism as SyntaxHighlighter } from "react-syntax-highlighter";
import { vscDarkPlus } from "react-syntax-highlighter/dist/esm/styles/prism";

// Code Block Component - React Docs Style with Beautiful Syntax Highlighting
function CodeBlock({
  title,
  code,
  description,
  language = "bash",
}: {
  title: string;
  code: string;
  description?: string;
  language?: string;
}) {
  const [copied, setCopied] = useState(false);

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(code);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error("Failed to copy text: ", err);
    }
  };

  // Determine if we should show line numbers (for longer code blocks)
  const shouldShowLineNumbers = code.split("\n").length > 10;

  return (
    <div className="my-6 sm:my-8">
      {/* Title and Description */}
      <div className="mb-4">
        <h4 className="text-lg sm:text-xl font-semibold text-gray-900 dark:text-white mb-2">
          {title}
        </h4>
        {description && (
          <p className="text-sm sm:text-base text-gray-600 dark:text-gray-400">
            {description}
          </p>
        )}
      </div>

      {/* Code Block Container */}
      <div className="relative">
        {/* Copy Button */}
        <button
          onClick={copyToClipboard}
          className="absolute top-3 right-3 z-10 flex items-center space-x-2 px-3 py-1.5 text-xs sm:text-sm bg-gray-700 hover:bg-gray-600 text-white rounded-md transition-colors shadow-lg"
        >
          {copied ? (
            <CheckCircle className="h-3 w-3 sm:h-4 sm:w-4" />
          ) : (
            <Copy className="h-3 w-3 sm:h-4 sm:w-4" />
          )}
          <span className="hidden sm:inline">
            {copied ? "Copied!" : "Copy"}
          </span>
        </button>

        <div className="rounded-xl border border-gray-700/50 overflow-hidden shadow-lg">
          <SyntaxHighlighter
            language={language}
            style={vscDarkPlus}
            customStyle={{
              margin: 0,
              padding: "20px",
              fontSize: "14px",
              lineHeight: "1.6",
              background: "#1e1e1e",
              borderRadius: "0",
            }}
            showLineNumbers={shouldShowLineNumbers}
            lineNumberStyle={{
              color: "#6b7280",
              paddingRight: "12px",
              fontSize: "12px",
              minWidth: "32px",
            }}
            wrapLines={true}
            wrapLongLines={true}
          >
            {code}
          </SyntaxHighlighter>
        </div>
      </div>
    </div>
  );
}

export default function NetworkPage() {
  const [activeTab, setActiveTab] = useState("dhcp");

  const tabs = [
    { id: "dhcp", label: "DHCP", icon: Wifi },
    { id: "topology", label: "Network Topology", icon: Network },
    { id: "protocols", label: "Protocols", icon: Globe },
    { id: "models", label: "TCP/IP & OSI Model", icon: Layers },
    { id: "addresses", label: "MAC vs IP Address", icon: Router },
    { id: "vlan", label: "VLAN Configuration", icon: Network },
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8 lg:py-12">
        <div className="space-y-6 sm:space-y-8 lg:space-y-12">
          {/* Header */}
          <div className="text-center space-y-4">
            <h1 className="text-2xl sm:text-3xl lg:text-4xl xl:text-5xl font-bold text-gray-900 dark:text-white">
              Network Configuration Guide
            </h1>
            <p className="text-base sm:text-lg lg:text-xl text-gray-600 dark:text-gray-300 max-w-4xl mx-auto leading-relaxed">
              Comprehensive guide covering DHCP, network topologies, protocols,
              TCP/IP & OSI models, MAC vs IP addressing, and VLAN configuration
              with practical Cisco examples.
            </p>
          </div>

          {/* Navigation Tabs */}
          <div className="flex flex-wrap justify-center gap-2 sm:gap-3 p-2 bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center space-x-2 px-3 sm:px-4 py-2 sm:py-3 rounded-lg transition-all text-sm sm:text-base ${
                    activeTab === tab.id
                      ? "bg-blue-600 text-white shadow-md transform scale-105"
                      : "text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700"
                  }`}
                >
                  <Icon className="h-4 w-4 sm:h-5 sm:w-5" />
                  <span className="font-medium hidden sm:inline">
                    {tab.label}
                  </span>
                  <span className="font-medium sm:hidden">
                    {tab.label.split(" ")[0]}
                  </span>
                </button>
              );
            })}
          </div>

          {/* Content */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
            <div className="p-4 sm:p-6 lg:p-8">
              {activeTab === "dhcp" && <DHCPSection />}
              {activeTab === "topology" && <TopologySection />}
              {activeTab === "protocols" && <ProtocolsSection />}
              {activeTab === "models" && <ModelsSection />}
              {activeTab === "addresses" && <AddressesSection />}
              {activeTab === "vlan" && <VLANSection />}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// DHCP Section
function DHCPSection() {
  return (
    <div className="space-y-6 sm:space-y-8">
      <div className="text-center sm:text-left">
        <h2 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 dark:text-white">
          DHCP Configuration & Management
        </h2>
        <p className="mt-2 text-sm sm:text-base text-gray-600 dark:text-gray-400">
          Learn how to configure and manage DHCP servers for automatic IP
          address assignment
        </p>
      </div>

      <div className="grid gap-6 lg:gap-8">
        {/* DHCP Overview */}
        <div className="bg-blue-50 dark:bg-gray-700 border border-blue-200 dark:border-blue-600 p-4 sm:p-6 rounded-lg">
          <h3 className="text-lg sm:text-xl font-semibold mb-4 text-blue-800 dark:text-blue-200">
            🌐 DHCP Overview
          </h3>
          <div className="grid gap-4 sm:gap-6 md:grid-cols-2">
            <div className="bg-white dark:bg-gray-800 p-4 rounded border">
              <h4 className="font-semibold text-gray-900 dark:text-white mb-2 text-sm sm:text-base">
                What is DHCP?
              </h4>
              <p className="text-gray-600 dark:text-gray-400 text-xs sm:text-sm">
                Dynamic Host Configuration Protocol automatically assigns IP
                addresses, subnet masks, default gateways, and DNS servers to
                network devices.
              </p>
            </div>
            <div className="bg-white dark:bg-gray-800 p-4 rounded border">
              <h4 className="font-semibold text-gray-900 dark:text-white mb-2">
                DHCP Process (DORA)
              </h4>
              <p className="text-gray-600 dark:text-gray-400 text-sm">
                <strong>Discover:</strong> Client broadcasts request for IP
                address
                <br />
                <strong>Offer:</strong> DHCP server offers available IP address
                <br />
                <strong>Request:</strong> Client requests the offered IP address
                <br />
                <strong>Acknowledge:</strong> Server confirms and assigns the IP
              </p>
            </div>
            <div className="bg-white dark:bg-gray-800 p-4 rounded border">
              <h4 className="font-semibold text-gray-900 dark:text-white mb-2">
                DHCP Components
              </h4>
              <p className="text-gray-600 dark:text-gray-400 text-sm">
                <strong>Scope:</strong> Range of IP addresses available for
                assignment
                <br />
                <strong>Lease:</strong> Duration for which IP address is
                assigned
                <br />
                <strong>Reservation:</strong> Specific IP assigned to specific
                MAC address
              </p>
            </div>
            <div className="bg-white dark:bg-gray-800 p-4 rounded border">
              <h4 className="font-semibold text-gray-900 dark:text-white mb-2">
                DHCP Benefits
              </h4>
              <p className="text-gray-600 dark:text-gray-400 text-sm">
                Automatic IP assignment, centralized management, reduced
                configuration errors, efficient IP address utilization, easy
                network changes
              </p>
            </div>
          </div>
        </div>

        <CodeBlock
          title="DHCP Server Configuration (Linux)"
          description="Basic DHCP server configuration using ISC DHCP"
          language="apache"
          code={`# /etc/dhcp/dhcpd.conf

# Global settings
default-lease-time 600;
max-lease-time 7200;
authoritative;

# DNS settings
option domain-name "example.com";
option domain-name-servers *******, *******;

# Subnet declaration
subnet *********** netmask ************* {
    range ***********00 *************;
    option routers ***********;
    option broadcast-address *************;
    default-lease-time 600;
    max-lease-time 7200;
}

# Static IP reservation
host server1 {
    hardware ethernet 00:11:22:33:44:55;
    fixed-address ************;
}

# Multiple subnets
subnet *********** netmask ************* {
    range ************ *************;
    option routers ***********;
    option domain-name-servers ***********0, *******;
}`}
        />

        <CodeBlock
          title="DHCP Client Configuration"
          description="DHCP client configuration and commands"
          language="bash"
          code={`# Windows DHCP client commands
ipconfig /release          # Release current IP lease
ipconfig /renew            # Renew IP lease
ipconfig /all              # Show detailed network configuration
ipconfig /displaydns       # Display DNS cache
ipconfig /flushdns         # Clear DNS cache

# Linux DHCP client commands
sudo dhclient -r eth0      # Release lease for eth0
sudo dhclient eth0         # Request new lease for eth0
sudo dhclient -v eth0      # Verbose output

# Check DHCP lease information (Linux)
cat /var/lib/dhcp/dhclient.leases

# NetworkManager (modern Linux)
nmcli con up "Wired connection 1"
nmcli con down "Wired connection 1"

# Check current IP configuration
ip addr show               # Linux
ifconfig                   # Linux/Mac
ipconfig                   # Windows`}
        />

        <CodeBlock
          title="DHCP Troubleshooting Commands"
          description="Commands for troubleshooting DHCP issues"
          language="bash"
          code={`# Check DHCP server status (Linux)
sudo systemctl status isc-dhcp-server
sudo systemctl restart isc-dhcp-server
sudo systemctl start isc-dhcp-server

# View DHCP server logs
sudo tail -f /var/log/syslog | grep dhcp
sudo journalctl -u isc-dhcp-server -f

# Test DHCP server configuration
sudo dhcpd -t -cf /etc/dhcp/dhcpd.conf

# Check DHCP leases
sudo cat /var/lib/dhcp/dhcpd.leases

# Monitor DHCP traffic
sudo tcpdump -i eth0 port 67 or port 68

# Check if DHCP ports are open
netstat -ulnp | grep :67
netstat -ulnp | grep :68

# Verify network connectivity
ping ***********           # Test gateway
ping *******               # Test external connectivity
nslookup google.com        # Test DNS resolution`}
        />
      </div>
    </div>
  );
}

// Network Topology Section
function TopologySection() {
  return (
    <div className="space-y-6 sm:space-y-8">
      <div className="text-center sm:text-left">
        <h2 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 dark:text-white">
          Network Topology Types & Characteristics
        </h2>
        <p className="mt-2 text-sm sm:text-base text-gray-600 dark:text-gray-400">
          Explore different network topologies and their practical applications
        </p>
      </div>

      <div className="grid gap-6 lg:gap-8">
        {/* Topology Overview */}
        <div className="bg-green-50 dark:bg-gray-700 border border-green-200 dark:border-green-600 p-4 sm:p-6 rounded-lg">
          <h3 className="text-lg sm:text-xl font-semibold mb-4 text-green-800 dark:text-green-200">
            🏗️ Network Topology Overview
          </h3>
          <div className="grid gap-4 sm:gap-6 md:grid-cols-2">
            <div className="space-y-3">
              <h4 className="font-semibold text-green-800 dark:text-green-200 text-sm sm:text-base">
                Physical Topology
              </h4>
              <ul className="text-xs sm:text-sm text-green-700 dark:text-green-300 space-y-1">
                <li>• Actual physical layout of network cables</li>
                <li>• How devices are physically connected</li>
                <li>• Cable routing and infrastructure</li>
                <li>• Hardware placement and connections</li>
              </ul>
            </div>
            <div className="space-y-3">
              <h4 className="font-semibold text-green-800 dark:text-green-200 text-sm sm:text-base">
                Logical Topology
              </h4>
              <ul className="text-xs sm:text-sm text-green-700 dark:text-green-300 space-y-1">
                <li>• How data flows through the network</li>
                <li>• Communication paths between devices</li>
                <li>• Protocol-based connections</li>
                <li>• Virtual network structures</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Topology Types */}
        <div className="grid gap-4 sm:gap-6 md:grid-cols-2 lg:grid-cols-2">
          <div className="bg-white dark:bg-gray-800 p-4 sm:p-6 rounded-lg border shadow-sm hover:shadow-md transition-shadow">
            <h4 className="font-semibold text-gray-900 dark:text-white mb-3 text-sm sm:text-base">
              🌟 Star Topology
            </h4>
            <div className="text-center mb-4">
              <div className="inline-block bg-blue-100 dark:bg-blue-900 p-3 sm:p-4 rounded-lg">
                <div className="text-xl sm:text-2xl">🌟</div>
                <div className="text-xs sm:text-sm mt-1 text-blue-800 dark:text-blue-200">
                  Central Hub/Switch
                </div>
              </div>
            </div>
            <div className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 space-y-2">
              <p>
                <strong>Description:</strong> All devices connect to a central
                hub or switch
              </p>
              <p>
                <strong>Pros:</strong> Easy to manage, fault isolation, scalable
              </p>
              <p>
                <strong>Cons:</strong> Single point of failure at center
              </p>
              <p>
                <strong>Use Cases:</strong> Most common in LANs, Ethernet
                networks
              </p>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 p-4 sm:p-6 rounded-lg border shadow-sm hover:shadow-md transition-shadow">
            <h4 className="font-semibold text-gray-900 dark:text-white mb-3 text-sm sm:text-base">
              🚌 Bus Topology
            </h4>
            <div className="text-center mb-4">
              <div className="inline-block bg-red-100 dark:bg-red-900 p-3 sm:p-4 rounded-lg">
                <div className="text-xl sm:text-2xl">🚌</div>
                <div className="text-xs sm:text-sm mt-1 text-red-800 dark:text-red-200">
                  Linear Bus
                </div>
              </div>
            </div>
            <div className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 space-y-2">
              <p>
                <strong>Description:</strong> All devices share a single
                communication line
              </p>
              <p>
                <strong>Pros:</strong> Simple, cost-effective, easy to implement
              </p>
              <p>
                <strong>Cons:</strong> Collision domain, limited distance,
                single failure point
              </p>
              <p>
                <strong>Use Cases:</strong> Legacy Ethernet (10Base2, 10Base5)
              </p>
            </div>
          </div>
        </div>

        {/* More Topology Types */}
        <div className="grid gap-4 sm:gap-6 md:grid-cols-2 lg:grid-cols-2">
          <div className="bg-white dark:bg-gray-800 p-4 sm:p-6 rounded-lg border shadow-sm hover:shadow-md transition-shadow">
            <h4 className="font-semibold text-gray-900 dark:text-white mb-3 text-sm sm:text-base">
              ⭕ Ring Topology
            </h4>
            <div className="text-center mb-4">
              <div className="inline-block bg-yellow-100 dark:bg-yellow-900 p-3 sm:p-4 rounded-lg">
                <div className="text-xl sm:text-2xl">⭕</div>
                <div className="text-xs sm:text-sm mt-1 text-yellow-800 dark:text-yellow-200">
                  Circular Path
                </div>
              </div>
            </div>
            <div className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 space-y-2">
              <p>
                <strong>Description:</strong> Devices connected in a circular
                fashion
              </p>
              <p>
                <strong>Pros:</strong> Equal access, predictable performance
              </p>
              <p>
                <strong>Cons:</strong> Single break affects entire network
              </p>
              <p>
                <strong>Use Cases:</strong> Token Ring, FDDI networks
              </p>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 p-4 sm:p-6 rounded-lg border shadow-sm hover:shadow-md transition-shadow">
            <h4 className="font-semibold text-gray-900 dark:text-white mb-3 text-sm sm:text-base">
              🕸️ Mesh Topology
            </h4>
            <div className="text-center mb-4">
              <div className="inline-block bg-green-100 dark:bg-green-900 p-3 sm:p-4 rounded-lg">
                <div className="text-xl sm:text-2xl">🕸️</div>
                <div className="text-xs sm:text-sm mt-1 text-green-800 dark:text-green-200">
                  Interconnected
                </div>
              </div>
            </div>
            <div className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 space-y-2">
              <p>
                <strong>Description:</strong> Every device connects to every
                other device
              </p>
              <p>
                <strong>Pros:</strong> High redundancy, fault tolerance, no
                single point of failure
              </p>
              <p>
                <strong>Cons:</strong> Complex, expensive, difficult to manage
              </p>
              <p>
                <strong>Use Cases:</strong> Critical networks, WANs, Internet
                backbone
              </p>
            </div>
          </div>
        </div>

        <CodeBlock
          title="Network Topology Comparison"
          description="Comparison table of different network topologies"
          language="markdown"
          code={`# Network Topology Comparison

Topology    | Cost | Reliability | Scalability | Performance | Use Case
------------|------|-------------|-------------|-------------|----------
Star        | Low  | Medium      | High        | Good        | LANs, Office networks
Bus         | Low  | Low         | Low         | Poor        | Legacy networks
Ring        | Med  | Medium      | Medium      | Good        | Token Ring, FDDI
Mesh        | High | Very High   | Medium      | Excellent   | WANs, Critical systems
Tree        | Med  | Medium      | High        | Good        | Large organizations
Hybrid      | High | High        | Very High   | Excellent   | Enterprise networks

# Topology Selection Factors:
1. Budget constraints
2. Reliability requirements
3. Network size and growth plans
4. Performance requirements
5. Maintenance complexity
6. Fault tolerance needs`}
        />
      </div>
    </div>
  );
}

// Protocols Section
function ProtocolsSection() {
  return (
    <div className="space-y-6 sm:space-y-8">
      <div className="text-center sm:text-left">
        <h2 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 dark:text-white">
          Network Protocols Overview
        </h2>
        <p className="mt-2 text-sm sm:text-base text-gray-600 dark:text-gray-400">
          Understanding the protocols that power network communication
        </p>
      </div>

      <div className="grid gap-6 lg:gap-8">
        {/* Protocol Categories */}
        <div className="bg-purple-50 dark:bg-gray-700 border border-purple-200 dark:border-purple-600 p-4 sm:p-6 rounded-lg">
          <h3 className="text-lg sm:text-xl font-semibold mb-4 text-purple-800 dark:text-purple-200">
            🌐 Protocol Categories
          </h3>

          <div className="grid gap-4 sm:gap-6 lg:grid-cols-2">
            <div className="bg-white dark:bg-gray-800 p-4 sm:p-6 rounded-lg border shadow-sm">
              <h4 className="font-semibold text-gray-900 dark:text-white mb-3 text-sm sm:text-base">
                Application Layer Protocols
              </h4>
              <div className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 space-y-2">
                <p>
                  <strong>HTTP/HTTPS:</strong> Web browsing (Port 80/443)
                </p>
                <p>
                  <strong>FTP:</strong> File transfer (Port 21)
                </p>
                <p>
                  <strong>SMTP:</strong> Email sending (Port 25)
                </p>
                <p>
                  <strong>POP3/IMAP:</strong> Email retrieval (Port 110/143)
                </p>
                <p>
                  <strong>DNS:</strong> Domain name resolution (Port 53)
                </p>
                <p>
                  <strong>DHCP:</strong> IP address assignment (Port 67/68)
                </p>
                <p>
                  <strong>SSH:</strong> Secure remote access (Port 22)
                </p>
                <p>
                  <strong>Telnet:</strong> Remote (Port 23)
                </p>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 p-4 sm:p-6 rounded-lg border shadow-sm">
              <h4 className="font-semibold text-gray-900 dark:text-white mb-3 text-sm sm:text-base">
                Transport Layer Protocols
              </h4>
              <div className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 space-y-2">
                <p>
                  <strong>TCP:</strong> Reliable, connection-oriented (Port
                  varies)
                </p>
                <p>
                  <strong>UDP:</strong> Fast, connectionless (Port varies)
                </p>
                <p>
                  <strong>SCTP:</strong> Stream Control Transmission Protocol
                </p>
              </div>
            </div>
          </div>

          <div className="grid gap-4 sm:gap-6 lg:grid-cols-2">
            <div className="bg-white dark:bg-gray-800 p-4 sm:p-6 rounded-lg border shadow-sm">
              <h4 className="font-semibold text-gray-900 dark:text-white mb-3 text-sm sm:text-base">
                Network Layer Protocols
              </h4>
              <div className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 space-y-2">
                <p>
                  <strong>IP (IPv4/IPv6):</strong> Internet Protocol
                </p>
                <p>
                  <strong>ICMP:</strong> Internet Control Message Protocol
                </p>
                <p>
                  <strong>IGMP:</strong> Internet Group Management Protocol
                </p>
                <p>
                  <strong>OSPF:</strong> Open Shortest Path First
                </p>
                <p>
                  <strong>BGP:</strong> Border Gateway Protocol
                </p>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 p-4 sm:p-6 rounded-lg border shadow-sm">
              <h4 className="font-semibold text-gray-900 dark:text-white mb-3 text-sm sm:text-base">
                Data Link Layer Protocols
              </h4>
              <div className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 space-y-2">
                <p>
                  <strong>Ethernet:</strong> Most common LAN protocol
                </p>
                <p>
                  <strong>Wi-Fi (802.11):</strong> Wireless LAN protocol
                </p>
                <p>
                  <strong>PPP:</strong> Point-to-Point Protocol
                </p>
                <p>
                  <strong>Frame Relay:</strong> WAN protocol
                </p>
                <p>
                  <strong>ATM:</strong> Asynchronous Transfer Mode
                </p>
              </div>
            </div>
          </div>
        </div>

        <CodeBlock
          title="Common Protocol Port Numbers"
          description="Standard port numbers for common network protocols"
          language="yaml"
          code={`# Well-Known Ports (0-1023)
FTP Data        20/tcp
FTP Control     21/tcp
SSH             22/tcp
Telnet          23/tcp
SMTP            25/tcp
DNS             53/tcp,udp
DHCP Server     67/udp
DHCP Client     68/udp
HTTP            80/tcp
POP3            110/tcp
NTP             123/udp
IMAP            143/tcp
SNMP            161/udp
HTTPS           443/tcp
SMTPS           465/tcp
IMAPS           993/tcp
POP3S           995/tcp

# Registered Ports (1024-49151)
MySQL           3306/tcp
RDP             3389/tcp
PostgreSQL      5432/tcp
VNC             5900/tcp

# Dynamic/Private Ports (49152-65535)
Used for client connections and temporary services`}
        />

        <CodeBlock
          title="Protocol Examples and Usage"
          description="Practical examples of how different protocols work together"
          language="markdown"
          code={`# Web Browsing Example (HTTP/HTTPS)
1. DNS Resolution: Client queries DNS server for www.example.com
2. TCP Connection: Client establishes TCP connection to server:80/443
3. HTTP Request: GET /index.html HTTP/1.1
4. HTTP Response: Server sends HTML content
5. Connection Close: TCP connection terminated

# Email Sending Example (SMTP)
1. Client connects to SMTP server on port 25
2. HELO/EHLO command to identify client
3. MAIL FROM: <EMAIL>
4. RCPT TO: <EMAIL>
5. DATA command followed by email content
6. QUIT to close connection

# File Transfer Example (FTP)
1. Control connection established on port 21
2. USER and PASS commands for authentication
3. PASV command for passive mode data connection
4. Data connection established on random port
5. File transfer commands (GET, PUT, LIST)
6. QUIT to close connections

# Remote Access Example (SSH)
1. TCP connection to server on port 22
2. SSH protocol negotiation
3. Key exchange and authentication
4. Encrypted tunnel established
5. Remote command execution or file transfer`}
        />
      </div>
    </div>
  );
}

// TCP/IP & OSI Models Section
function ModelsSection() {
  return (
    <div className="space-y-6 sm:space-y-8">
      <div className="text-center sm:text-left">
        <h2 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 dark:text-white">
          TCP/IP & OSI Model
        </h2>
        <p className="mt-2 text-sm sm:text-base text-gray-600 dark:text-gray-400">
          Understanding the fundamental networking models and how they work
          together
        </p>
      </div>

      <div className="grid gap-6 lg:gap-8">
        {/* OSI Model */}
        <div className="bg-blue-50 dark:bg-gray-700 border border-blue-200 dark:border-blue-600 p-4 sm:p-6 rounded-lg">
          <h3 className="text-lg sm:text-xl font-semibold mb-4 text-blue-800 dark:text-blue-200">
            🏗️ OSI 7-Layer Model
          </h3>
          <div className="grid gap-4 sm:gap-6 lg:grid-cols-2">
            <div className="space-y-3">
              <div className="bg-white dark:bg-gray-800 p-3 sm:p-4 rounded-lg border shadow-sm">
                <h4 className="font-semibold text-red-600 dark:text-red-400 text-sm sm:text-base">
                  Layer 7 - Application
                </h4>
                <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400">
                  HTTP, HTTPS, FTP, SMTP, DNS
                </p>
              </div>
              <div className="bg-white dark:bg-gray-800 p-3 sm:p-4 rounded-lg border shadow-sm">
                <h4 className="font-semibold text-orange-600 dark:text-orange-400 text-sm sm:text-base">
                  Layer 6 - Presentation
                </h4>
                <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400">
                  Encryption, Compression, Translation
                </p>
              </div>
              <div className="bg-white dark:bg-gray-800 p-3 sm:p-4 rounded-lg border shadow-sm">
                <h4 className="font-semibold text-yellow-600 dark:text-yellow-400 text-sm sm:text-base">
                  Layer 5 - Session
                </h4>
                <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400">
                  Session Management, NetBIOS
                </p>
              </div>
              <div className="bg-white dark:bg-gray-800 p-3 sm:p-4 rounded-lg border shadow-sm">
                <h4 className="font-semibold text-green-600 dark:text-green-400 text-sm sm:text-base">
                  Layer 4 - Transport
                </h4>
                <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400">
                  TCP, UDP, Port Numbers
                </p>
              </div>
            </div>
            <div className="space-y-3">
              <div className="bg-white dark:bg-gray-800 p-3 sm:p-4 rounded-lg border shadow-sm">
                <h4 className="font-semibold text-blue-600 dark:text-blue-400 text-sm sm:text-base">
                  Layer 3 - Network
                </h4>
                <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400">
                  IP, ICMP, Routing, Logical Addressing
                </p>
              </div>
              <div className="bg-white dark:bg-gray-800 p-3 sm:p-4 rounded-lg border shadow-sm">
                <h4 className="font-semibold text-indigo-600 dark:text-indigo-400 text-sm sm:text-base">
                  Layer 2 - Data Link
                </h4>
                <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400">
                  Ethernet, Wi-Fi, MAC Addresses, Switching
                </p>
              </div>
              <div className="bg-white dark:bg-gray-800 p-3 sm:p-4 rounded-lg border shadow-sm">
                <h4 className="font-semibold text-purple-600 dark:text-purple-400 text-sm sm:text-base">
                  Layer 1 - Physical
                </h4>
                <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400">
                  Cables, Hubs, Repeaters, Electrical Signals
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* TCP/IP Model */}
        <div className="bg-green-50 dark:bg-gray-700 border border-green-200 dark:border-green-600 p-4 sm:p-6 rounded-lg">
          <h3 className="text-lg sm:text-xl font-semibold mb-4 text-green-800 dark:text-green-200">
            🌐 TCP/IP 4-Layer Model
          </h3>
          <div className="grid gap-4 sm:gap-6 lg:grid-cols-2">
            <div className="space-y-3">
              <div className="bg-white dark:bg-gray-800 p-3 sm:p-4 rounded-lg border shadow-sm">
                <h4 className="font-semibold text-blue-600 dark:text-blue-400 text-sm sm:text-base">
                  Layer 4 - Application
                </h4>
                <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400">
                  Combines OSI Layers 5, 6, 7<br />
                  HTTP, FTP, SMTP, DNS, DHCP
                </p>
              </div>
              <div className="bg-white dark:bg-gray-800 p-3 sm:p-4 rounded-lg border shadow-sm">
                <h4 className="font-semibold text-green-600 dark:text-green-400 text-sm sm:text-base">
                  Layer 3 - Transport
                </h4>
                <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400">
                  Same as OSI Layer 4<br />
                  TCP, UDP, Port-to-Port delivery
                </p>
              </div>
            </div>
            <div className="space-y-3">
              <div className="bg-white dark:bg-gray-800 p-3 sm:p-4 rounded-lg border shadow-sm">
                <h4 className="font-semibold text-yellow-600 dark:text-yellow-400 text-sm sm:text-base">
                  Layer 2 - Internet
                </h4>
                <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400">
                  Same as OSI Layer 3<br />
                  IP, ICMP, ARP, Routing
                </p>
              </div>
              <div className="bg-white dark:bg-gray-800 p-3 sm:p-4 rounded-lg border shadow-sm">
                <h4 className="font-semibold text-purple-600 dark:text-purple-400 text-sm sm:text-base">
                  Layer 1 - Network Access
                </h4>
                <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400">
                  Combines OSI Layers 1, 2<br />
                  Ethernet, Wi-Fi, Physical transmission
                </p>
              </div>
            </div>
          </div>
        </div>

        <CodeBlock
          title="OSI vs TCP/IP Model Comparison"
          description="Side-by-side comparison of OSI and TCP/IP models"
          language="markdown"
          code={`# OSI Model vs TCP/IP Model

OSI Layer               | TCP/IP Layer        | Protocols/Examples
------------------------|--------------------|-----------------------
7. Application          | 4. Application     | HTTP, HTTPS, FTP, SMTP
6. Presentation         |                    | SSL/TLS, JPEG, GIF
5. Session              |                    | NetBIOS, RPC, SQL
4. Transport            | 3. Transport       | TCP, UDP
3. Network              | 2. Internet        | IP, ICMP, ARP, OSPF
2. Data Link            | 1. Network Access  | Ethernet, Wi-Fi, PPP
1. Physical             |                    | Cables, Hubs, Repeaters

# Key Differences:
- OSI: 7 layers, theoretical model
- TCP/IP: 4 layers, practical implementation
- OSI: More detailed separation of concerns
- TCP/IP: Simpler, widely used in practice

# Data Encapsulation Process:
Application Data → Segments → Packets → Frames → Bits`}
        />

        <CodeBlock
          title="Network Communication Example"
          description="Step-by-step example of data flow through the layers"
          language="markdown"
          code={`# Example: Sending an Email (SMTP)

Application Layer (OSI 7 / TCP/IP 4):
- User composes email in email client
- SMTP protocol formats the message

Presentation Layer (OSI 6):
- Email encoded (UTF-8, Base64 for attachments)
- Encryption applied if using SMTPS

Session Layer (OSI 5):
- SMTP session established with mail server
- Authentication and session management

Transport Layer (OSI 4 / TCP/IP 3):
- TCP adds port numbers (source: random, dest: 25)
- Data segmented, sequence numbers added
- Reliability and flow control

Network Layer (OSI 3 / TCP/IP 2):
- IP header added with source/destination IP
- Routing decisions made
- Packet forwarding through routers

Data Link Layer (OSI 2 / TCP/IP 1):
- Ethernet header added with MAC addresses
- Frame check sequence for error detection
- Switching within local network

Physical Layer (OSI 1):
- Electrical signals transmitted over cable
- Bits converted to physical medium signals`}
        />
      </div>
    </div>
  );
}

// MAC vs IP Address Section
function AddressesSection() {
  return (
    <div className="space-y-6 sm:space-y-8">
      <div className="text-center sm:text-left">
        <h2 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 dark:text-white">
          MAC vs IP Address
        </h2>
        <p className="mt-2 text-sm sm:text-base text-gray-600 dark:text-gray-400">
          Understanding the differences between physical and logical addressing
        </p>
      </div>

      <div className="grid gap-6 lg:gap-8">
        {/* Address Comparison */}
        <div className="bg-orange-50 dark:bg-gray-700 border border-orange-200 dark:border-orange-600 p-4 sm:p-6 rounded-lg">
          <h3 className="text-lg sm:text-xl font-semibold mb-4 text-orange-800 dark:text-orange-200">
            🏷️ Address Types Comparison
          </h3>

          <div className="grid gap-4 sm:gap-6 lg:grid-cols-2">
            <div className="bg-white dark:bg-gray-800 p-4 sm:p-6 rounded-lg border shadow-sm">
              <h4 className="font-semibold text-blue-600 dark:text-blue-400 mb-3 text-sm sm:text-base">
                MAC Address (Physical)
              </h4>
              <div className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 space-y-2">
                <p>
                  <strong>Full Name:</strong> Media Access Control Address
                </p>
                <p>
                  <strong>Layer:</strong> Data Link Layer (Layer 2)
                </p>
                <p>
                  <strong>Format:</strong> 48-bit (6 bytes) hexadecimal
                </p>
                <p>
                  <strong>Example:</strong> 00:1B:44:11:3A:B7
                </p>
                <p>
                  <strong>Scope:</strong> Local network segment
                </p>
                <p>
                  <strong>Assignment:</strong> Manufacturer assigned
                </p>
                <p>
                  <strong>Changeability:</strong> Hardware-based (can be
                  spoofed)
                </p>
                <p>
                  <strong>Purpose:</strong> Local network communication
                </p>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 p-4 sm:p-6 rounded-lg border shadow-sm">
              <h4 className="font-semibold text-green-600 dark:text-green-400 mb-3 text-sm sm:text-base">
                IP Address (Logical)
              </h4>
              <div className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 space-y-2">
                <p>
                  <strong>Full Name:</strong> Internet Protocol Address
                </p>
                <p>
                  <strong>Layer:</strong> Network Layer (Layer 3)
                </p>
                <p>
                  <strong>Format:</strong> 32-bit (IPv4) or 128-bit (IPv6)
                </p>
                <p>
                  <strong>Example:</strong> ***********00 or 2001:db8::1
                </p>
                <p>
                  <strong>Scope:</strong> Global internet or network
                </p>
                <p>
                  <strong>Assignment:</strong> Network administrator or DHCP
                </p>
                <p>
                  <strong>Changeability:</strong> Easily changeable
                </p>
                <p>
                  <strong>Purpose:</strong> End-to-end communication
                </p>
              </div>
            </div>
          </div>
        </div>

        <CodeBlock
          title="Address Format Examples"
          description="Different formats and examples of MAC and IP addresses"
          language="yaml"
          code={`# MAC Address Formats
Standard Format:    00:1B:44:11:3A:B7
Cisco Format:       001b.4411.3ab7
Windows Format:     00-1B-44-11-3A-B7
No Separators:      001B44113AB7

# MAC Address Structure
OUI (First 3 bytes):  00:1B:44  (Organizationally Unique Identifier)
NIC (Last 3 bytes):   11:3A:B7  (Network Interface Controller specific)

# IPv4 Address Examples
Private Ranges:
  Class A: 10.0.0.0/8        (10.0.0.0 - **************)
  Class B: **********/12     (********** - **************)
  Class C: ***********/16    (*********** - ***************)

Public Examples:
  Google DNS:     *******, *******
  Cloudflare:     *******, *******
  OpenDNS:        **************, **************

# IPv6 Address Examples
Loopback:           ::1
Link-local:         fe80::1
Global Unicast:     2001:db8:85a3::8a2e:370:7334
Multicast:          ff02::1`}
        />

        <CodeBlock
          title="Address Resolution Process"
          description="How MAC and IP addresses work together in network communication"
          language="markdown"
          code={`# ARP (Address Resolution Protocol) Process
1. Host A wants to send data to Host B (knows IP, needs MAC)
2. Host A checks ARP cache for IP-to-MAC mapping
3. If not found, Host A broadcasts ARP request:
   "Who has IP ***********00? Tell ************"
4. Host B responds with ARP reply:
   "***********00 is at MAC 00:1B:44:11:3A:B7"
5. Host A updates ARP cache and sends data

# Communication Scenarios

Local Network Communication:
- Source IP: ************      Dest IP: ***********00
- Source MAC: AA:BB:CC:DD:EE:FF Dest MAC: 00:1B:44:11:3A:B7
- Direct communication using both addresses

Remote Network Communication:
- Source IP: ************      Dest IP: *******
- Source MAC: AA:BB:CC:DD:EE:FF Dest MAC: 11:22:33:44:55:66 (Gateway)
- IP address stays same, MAC changes at each hop

# ARP Table Commands
Windows:    arp -a
Linux:      arp -a  or  ip neigh show
Clear ARP:  arp -d * (Windows)  or  ip neigh flush all (Linux)`}
        />

        <CodeBlock
          title="Practical Address Examples"
          description="Real-world scenarios showing MAC and IP address usage"
          language="markdown"
          code={`# Scenario 1: DHCP Assignment
1. Device boots up with MAC: 00:1B:44:11:3A:B7
2. DHCP Discover broadcast (MAC known, IP unknown)
3. DHCP server assigns IP: ***********00
4. Device now has both MAC and IP addresses

# Scenario 2: Network Troubleshooting
Problem: Can't reach ***********00
Steps:
1. ping ***********00          # Test IP connectivity
2. arp -a                      # Check if MAC is learned
3. If no ARP entry: Network/routing issue
4. If ARP entry exists: Higher layer problem

# Scenario 3: Security Considerations
MAC Filtering:
- Router allows only specific MAC addresses
- Provides basic security for wireless networks
- Can be bypassed by MAC spoofing

IP-based Security:
- Firewall rules based on IP addresses
- Access control lists (ACLs)
- More flexible and scalable

# Scenario 4: Network Monitoring
Switch MAC Table:
- Shows which MAC addresses are on which ports
- Used for switching decisions
- Command: show mac address-table (Cisco)

Router ARP Table:
- Shows IP-to-MAC mappings
- Used for local delivery decisions
- Command: show arp (Cisco) or arp -a (PC)`}
        />
      </div>
    </div>
  );
}

// VLAN Configuration Section
function VLANSection() {
  return (
    <div className="space-y-6 sm:space-y-8">
      <div className="text-center sm:text-left">
        <h2 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 dark:text-white">
          VLAN Configuration & Management
        </h2>
        <p className="mt-2 text-sm sm:text-base text-gray-600 dark:text-gray-400">
          Learn how to configure and manage VLANs on Cisco switches for network
          segmentation
        </p>
      </div>

      <div className="grid gap-6 lg:gap-8">
        {/* VLAN Concepts */}
        <div className="bg-green-50 dark:bg-gray-700 border border-green-200 dark:border-green-600 p-4 sm:p-6 rounded-lg">
          <h3 className="text-lg sm:text-xl font-semibold mb-4 text-green-800 dark:text-green-200">
            🔗 VLAN Concepts & Benefits
          </h3>
          <div className="grid gap-4 sm:gap-6 lg:grid-cols-2">
            <div className="space-y-3">
              <h4 className="font-semibold text-green-800 dark:text-green-200 text-sm sm:text-base">
                What is a VLAN?
              </h4>
              <ul className="text-xs sm:text-sm text-green-700 dark:text-green-300 space-y-1">
                <li>• Virtual Local Area Network</li>
                <li>• Logical segmentation of physical network</li>
                <li>• Broadcast domain separation</li>
                <li>• Layer 2 network virtualization</li>
              </ul>
            </div>
            <div className="space-y-3">
              <h4 className="font-semibold text-green-800 dark:text-green-200 text-sm sm:text-base">
                Benefits
              </h4>
              <ul className="text-xs sm:text-sm text-green-700 dark:text-green-300 space-y-1">
                <li>• Enhanced security</li>
                <li>• Reduced broadcast traffic</li>
                <li>• Flexible network design</li>
                <li>• Cost-effective segmentation</li>
              </ul>
            </div>
          </div>
        </div>

        <CodeBlock
          title="Cisco Switch VLAN Configuration"
          description="Basic VLAN setup on Cisco switches with access and trunk ports"
          language="cisco"
          code={`! Enter global configuration mode
Switch> enable
Switch# configure 

! Create VLANs
Switch(config)# vlan 10
Switch(config-vlan)# name Sales
Switch(config-vlan)# exit

Switch(config)# vlan 20
Switch(config-vlan)# name Engineering
Switch(config-vlan)# exit

Switch(config)# vlan 30
Switch(config-vlan)# name Management
Switch(config-vlan)# exit

! Configure access ports
Switch(config)# interface range fastethernet 0/1-10
Switch(config-if-range)# switchport mode access
Switch(config-if-range)# switchport access vlan 10
Switch(config-if-range)# exit

Switch(config)# interface range fastethernet 0/11-20
Switch(config-if-range)# switchport mode access
Switch(config-if-range)# switchport access vlan 20
Switch(config-if-range)# exit

! Configure trunk port
Switch(config)# interface gigabitethernet 0/1
Switch(config-if)# switchport mode trunk
Switch(config-if)# switchport trunk allowed vlan 10,20,30
Switch(config-if)# switchport trunk native vlan 1
Switch(config-if)# exit

! Save configuration
Switch(config)# exit
Switch# write memory`}
        />

        <CodeBlock
          title="Inter-VLAN Routing Configuration"
          description="Router-on-a-stick configuration for inter-VLAN communication"
          language="cisco"
          code={`! Router configuration for inter-VLAN routing
Router> enable
Router# configure 

! Configure subinterfaces for each VLAN
Router(config)# interface gigabitethernet 0/0.10
Router(config-subif)# encapsulation dot1Q 10
Router(config-subif)# ip address ************ *************
Router(config-subif)# exit

Router(config)# interface gigabitethernet 0/0.20
Router(config-subif)# encapsulation dot1Q 20
Router(config-subif)# ip address ************ *************
Router(config-subif)# exit

Router(config)# interface gigabitethernet 0/0.30
Router(config-subif)# encapsulation dot1Q 30
Router(config-subif)# ip address ************ *************
Router(config-subif)# exit

! Enable the physical interface
Router(config)# interface gigabitethernet 0/0
Router(config-if)# no shutdown
Router(config-if)# exit

! Optional: Configure DHCP for VLANs
Router(config)# ip dhcp pool VLAN10
Router(dhcp-config)# network ************ *************
Router(dhcp-config)# default-router ************
Router(dhcp-config)# dns-server ******* *******
Router(dhcp-config)# exit

Router(config)# ip dhcp pool VLAN20
Router(dhcp-config)# network ************ *************
Router(dhcp-config)# default-router ************
Router(dhcp-config)# dns-server ******* *******
Router(dhcp-config)# exit`}
        />

        <CodeBlock
          title="VLAN Verification Commands"
          description="Commands to verify and troubleshoot VLAN configuration"
          language="cisco"
          code={`! Show VLAN information
Switch# show vlan brief
Switch# show vlan id 10

! Show interface VLAN assignment
Switch# show interfaces switchport
Switch# show interfaces fastethernet 0/1 switchport

! Show trunk configuration
Switch# show interfaces trunk
Switch# show interfaces gigabitethernet 0/1 trunk

! Show MAC address table per VLAN
Switch# show mac address-table vlan 10

! Show spanning tree per VLAN
Switch# show spanning-tree vlan 10

! Debug VLAN operations (use carefully)
Switch# debug vlan packets

! On router - show subinterfaces
Router# show ip interface brief
Router# show interfaces gigabitethernet 0/0.10

! Show routing table
Router# show ip route

! Test connectivity between VLANs
Router# ping ************0 source ************`}
        />

        {/* VLAN Types and Best Practices */}
        <div className="bg-blue-50 dark:bg-gray-700 border border-blue-200 dark:border-blue-600 p-4 sm:p-6 rounded-lg">
          <h3 className="text-lg sm:text-xl font-semibold mb-4 text-blue-800 dark:text-blue-200">
            📋 VLAN Types & Best Practices
          </h3>
          <div className="grid gap-4 sm:gap-6 lg:grid-cols-2">
            <div className="bg-white dark:bg-gray-800 p-4 sm:p-6 rounded-lg border shadow-sm">
              <h4 className="font-semibold text-gray-900 dark:text-white mb-3 text-sm sm:text-base">
                VLAN Types
              </h4>
              <div className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 space-y-2">
                <p>
                  <strong>Data VLAN:</strong> Carries user-generated traffic
                </p>
                <p>
                  <strong>Voice VLAN:</strong> Dedicated for VoIP traffic
                </p>
                <p>
                  <strong>Management VLAN:</strong> For network device
                  management
                </p>
                <p>
                  <strong>Native VLAN:</strong> Untagged traffic on trunk links
                </p>
                <p>
                  <strong>Default VLAN:</strong> VLAN 1 (should not be used for
                  data)
                </p>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 p-4 sm:p-6 rounded-lg border shadow-sm">
              <h4 className="font-semibold text-gray-900 dark:text-white mb-3 text-sm sm:text-base">
                Best Practices
              </h4>
              <div className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 space-y-2">
                <p>
                  <strong>Security:</strong> Don&apos;t use VLAN 1 for data
                  traffic
                </p>
                <p>
                  <b>Naming:</b> Use descriptive VLAN names
                </p>
                <p>
                  <strong>Documentation:</strong> Maintain VLAN documentation
                </p>
                <p>
                  <strong>Planning:</strong> Plan VLAN numbering scheme
                </p>
                <p>
                  <strong>Monitoring:</strong> Regular VLAN performance
                  monitoring
                </p>
              </div>
            </div>
          </div>
        </div>

        <CodeBlock
          title="Advanced VLAN Configuration"
          description="Advanced VLAN features and troubleshooting scenarios"
          language="cisco"
          code={`! Voice VLAN Configuration
Switch(config)# interface fastethernet 0/5
Switch(config-if)# switchport mode access
Switch(config-if)# switchport access vlan 10
Switch(config-if)# switchport voice vlan 100
Switch(config-if)# exit

! VLAN Pruning on Trunk
Switch(config)# interface gigabitethernet 0/1
Switch(config-if)# switchport trunk pruning vlan remove 50-60
Switch(config-if)# exit

! Private VLAN Configuration
Switch(config)# vlan 200
Switch(config-vlan)# private-vlan primary
Switch(config-vlan)# exit

Switch(config)# vlan 201
Switch(config-vlan)# private-vlan isolated
Switch(config-vlan)# exit

Switch(config)# vlan 200
Switch(config-vlan)# private-vlan association 201
Switch(config-vlan)# exit

! VLAN Access Control Lists
Switch(config)# ip access-list extended VLAN10_ACL
Switch(config-ext-nacl)# permit tcp ************ ********* any eq 80
Switch(config-ext-nacl)# permit tcp ************ ********* any eq 443
Switch(config-ext-nacl)# deny ip any any
Switch(config-ext-nacl)# exit

Switch(config)# interface vlan 10
Switch(config-if)# ip access-group VLAN10_ACL in
Switch(config-if)# exit

! Troubleshooting Commands
Switch# show vlan brief | include active
Switch# show interfaces status
Switch# show mac address-table dynamic
Switch# show spanning-tree summary`}
        />
      </div>
    </div>
  );
}
