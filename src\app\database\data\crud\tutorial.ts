// CRUD Operations Data - Tutorial Management System
export const crudData = {
  dbConnect: {
    title: "config.php - Database Connection",
    description: "This file handles the connection to your MySQL database",
    language: "php",
    code: `<?php
$servername = "localhost";
$username = "root";
$password = "";
$dbname = "tutorial_management";

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}
?>`,
  },
  indexPage: {
    title: "index.php - Homepage & Data Viewer",
    description:
      "Main interface with navigation links and comprehensive data display from all tables",
    language: "php",
    code: `<?php
include "config.php";
?>
<!DOCTYPE html>
<html>
<head>
    <title>Tutorial Management System</title>
    <link rel="stylesheet" href="tutorial-styles.css">
</head>
<body>
    <div class="container">
        <h1>🎓 Tutorial Management System</h1>

        <div class="nav-buttons">
            <a href="create.php" class="btn">➕ Add New Records</a>
            <a href="update.php" class="btn">✏️ Update Records</a>
            <a href="delete.php" class="btn">🗑️ Delete Records</a>
        </div>

        <!-- Tutors Section -->
        <div class="section">
            <h2>👨‍🏫 Tutors</h2>
            <?php
            $result = $conn->query("SELECT * FROM tbl_tutors ORDER BY TutorID");
            if ($result->num_rows > 0) {
                echo "<table>";
                echo "<tr><th>ID</th><th>Name</th><th>Email</th><th>Department</th></tr>";
                while($row = $result->fetch_assoc()) {
                    echo "<tr>
                            <td>{$row['TutorID']}</td>
                            <td>{$row['TutorName']}</td>
                            <td>{$row['TutorEmail']}</td>
                            <td>{$row['Department']}</td>
                        </tr>";
                }
                echo "</table>";
            } else {
                echo "<p>No tutors found.</p>";
            }
            ?>
        </div>

        <!-- Units Section -->
        <div class="section">
            <h2>📚 Units</h2>
            <?php
            $result = $conn->query("SELECT u.*, t.TutorName FROM tbl_units u
                                   LEFT JOIN tbl_tutors t ON u.TutorID = t.TutorID
                                   ORDER BY u.UnitID");
            if ($result->num_rows > 0) {
                echo "<table>";
                echo "<tr><th>ID</th><th>Unit Name</th><th>Tutor</th><th>Credits</th></tr>";
                while($row = $result->fetch_assoc()) {
                    echo "<tr>
                            <td>{$row['UnitID']}</td>
                            <td>{$row['UnitName']}</td>
                            <td>{$row['TutorName']}</td>
                            <td>{$row['Credits']}</td>
                        </tr>";
                }
                echo "</table>";
            } else {
                echo "<p>No units found.</p>";
            }
            ?>
        </div>

        <!-- Students Section -->
        <div class="section">
            <h2>👨‍🎓 Students</h2>
            <?php
            $result = $conn->query("SELECT * FROM tbl_students ORDER BY StudentID");
            if ($result->num_rows > 0) {
                echo "<table>";
                echo "<tr><th>ID</th><th>Name</th><th>Email</th><th>Phone</th></tr>";
                while($row = $result->fetch_assoc()) {
                    echo "<tr>
                            <td>{$row['StudentID']}</td>
                            <td>{$row['StudentName']}</td>
                            <td>{$row['StudentEmail']}</td>
                            <td>{$row['Phone']}</td>
                        </tr>";
                }
                echo "</table>";
            } else {
                echo "<p>No students found.</p>";
            }
            ?>
        </div>

        <!-- Enrollments Section -->
        <div class="section">
            <h2>📝 Enrollments</h2>
            <?php
            $result = $conn->query("SELECT e.*, s.StudentName, u.UnitName
                                   FROM tbl_enrollments e
                                   LEFT JOIN tbl_students s ON e.StudentID = s.StudentID
                                   LEFT JOIN tbl_units u ON e.UnitID = u.UnitID
                                   ORDER BY e.EnrollmentID");
            if ($result->num_rows > 0) {
                echo "<table>";
                echo "<tr><th>ID</th><th>Student</th><th>Unit</th><th>Enroll Date</th></tr>";
                while($row = $result->fetch_assoc()) {
                    echo "<tr>
                            <td>{$row['EnrollmentID']}</td>
                            <td>{$row['StudentName']}</td>
                            <td>{$row['UnitName']}</td>
                            <td>{$row['EnrollDate']}</td>
                        </tr>";
                }
                echo "</table>";
            } else {
                echo "<p>No enrollments found.</p>";
            }
            ?>
        </div>
    </div>
</body>
</html>`,
  },
  createPage: {
    title: "create.php - Add New Records",
    description:
      "Simple forms for adding tutors, units, students, and enrollments",
    language: "php",
    code: `<?php include "config.php"; ?>
<!DOCTYPE html>
<html>
<head>
    <title>Add Records</title>
    <link rel="stylesheet" href="tutorial-styles.css">
</head>
<body>
    <div class="container">
        <h1>➕ Add Records</h1>
        <a href="index.php" class="btn">← Back to Main</a>

        <!-- Add Tutor -->
        <h2>👨‍🏫 Add Tutor</h2>
        <form method="POST" action="create.php">
            <input type="text" name="tutor_name" placeholder="Tutor Name" required>
            <input type="email" name="tutor_email" placeholder="Email" required>
            <input type="text" name="department" placeholder="Department" required>
            <button type="submit" name="add_tutor" class="btn">Add Tutor</button>
        </form>

        <!-- Add Unit -->
        <h2>📚 Add Unit</h2>
        <form method="POST" action="create.php">
            <input type="text" name="unit_name" placeholder="Unit Name" required>
            <select name="tutor_id" required>
                <option value="">Select Tutor</option>
                <?php
                $tutors = $conn->query("SELECT TutorID, TutorName FROM tbl_tutors");
                while($tutor = $tutors->fetch_assoc()) {
                    echo "<option value='{$tutor['TutorID']}'>{$tutor['TutorName']}</option>";
                }
                ?>
            </select>
            <input type="number" name="credits" placeholder="Credits" min="1" max="6" required>
            <button type="submit" name="add_unit" class="btn">Add Unit</button>
        </form>

        <!-- Add Student -->
        <h2>👨‍🎓 Add Student</h2>
        <form method="POST" action="create.php">
            <input type="text" name="student_name" placeholder="Student Name" required>
            <input type="email" name="student_email" placeholder="Email" required>
            <input type="text" name="phone" placeholder="Phone" required>
            <button type="submit" name="add_student" class="btn">Add Student</button>
        </form>

        <!-- Enroll Student -->
        <h2>📝 Enroll Student</h2>
        <form method="POST" action="create.php">
            <select name="student_id" required>
                <option value="">Select Student</option>
                <?php
                $students = $conn->query("SELECT StudentID, StudentName FROM tbl_students");
                while($student = $students->fetch_assoc()) {
                    echo "<option value='{$student['StudentID']}'>{$student['StudentName']}</option>";
                }
                ?>
            </select>
            <select name="unit_id" required>
                <option value="">Select Unit</option>
                <?php
                $units = $conn->query("SELECT UnitID, UnitName FROM tbl_units");
                while($unit = $units->fetch_assoc()) {
                    echo "<option value='{$unit['UnitID']}'>{$unit['UnitName']}</option>";
                }
                ?>
            </select>
            <button type="submit" name="enroll_student" class="btn">Enroll Student</button>
        </form>

        <?php
        // Handle form submissions
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Add Tutor
            if (isset($_POST['add_tutor'])) {
                $name = $_POST['tutor_name'];
                $email = $_POST['tutor_email'];
                $department = $_POST['department'];

                $conn->query("INSERT INTO tbl_tutors (TutorName, TutorEmail, Department)
                              VALUES ('$name', '$email', '$department')");
                header("Location: create.php");
            }

            // Add Unit
            if (isset($_POST['add_unit'])) {
                $name = $_POST['unit_name'];
                $tutor_id = $_POST['tutor_id'];
                $credits = $_POST['credits'];

                $conn->query("INSERT INTO tbl_units (UnitName, TutorID, Credits)
                              VALUES ('$name', '$tutor_id', '$credits')");
                header("Location: create.php");
            }

            // Add Student
            if (isset($_POST['add_student'])) {
                $name = $_POST['student_name'];
                $email = $_POST['student_email'];
                $phone = $_POST['phone'];

                $conn->query("INSERT INTO tbl_students (StudentName, StudentEmail, Phone)
                              VALUES ('$name', '$email', '$phone')");
                header("Location: create.php");
            }

            // Enroll Student
            if (isset($_POST['enroll_student'])) {
                $student_id = $_POST['student_id'];
                $unit_id = $_POST['unit_id'];

                $conn->query("INSERT INTO tbl_enrollments (StudentID, UnitID, EnrollDate)
                              VALUES ('$student_id', '$unit_id', CURDATE())");
                header("Location: create.php");
            }
        }
        ?>
    </div>
</body>
</html>`,
  },
  updatePage: {
    title: "update.php - Edit Records",
    description:
      "Simple forms for updating tutors, units, students, and enrollments",
    language: "php",
    code: `<?php include "config.php"; ?>
<!DOCTYPE html>
<html>
<head>
    <title>Update Records</title>
    <link rel="stylesheet" href="tutorial-styles.css">
</head>
<body>
    <div class="container">
        <h1>✏️ Update Records</h1>
        <a href="index.php" class="btn">← Back to Main</a>

        <!-- Update Tutor -->
        <h2>👨‍🏫 Update Tutor</h2>
        <form method="POST" action="update.php">
            <select name="tutor_id" required>
                <option value="">Select Tutor to Update</option>
                <?php
                $tutors = $conn->query("SELECT TutorID, TutorName FROM tbl_tutors");
                while($tutor = $tutors->fetch_assoc()) {
                    echo "<option value='{$tutor['TutorID']}'>{$tutor['TutorName']}</option>";
                }
                ?>
            </select>
            <input type="text" name="tutor_name" placeholder="New Tutor Name" required>
            <input type="email" name="tutor_email" placeholder="New Email" required>
            <input type="text" name="department" placeholder="New Department" required>
            <button type="submit" name="update_tutor" class="btn">Update Tutor</button>
        </form>

        <!-- Update Unit -->
        <h2>📚 Update Unit</h2>
        <form method="POST" action="update.php">
            <select name="unit_id" required>
                <option value="">Select Unit to Update</option>
                <?php
                $units = $conn->query("SELECT UnitID, UnitName FROM tbl_units");
                while($unit = $units->fetch_assoc()) {
                    echo "<option value='{$unit['UnitID']}'>{$unit['UnitName']}</option>";
                }
                ?>
            </select>
            <input type="text" name="unit_name" placeholder="New Unit Name" required>
            <select name="tutor_id" required>
                <option value="">Select New Tutor</option>
                <?php
                $tutors = $conn->query("SELECT TutorID, TutorName FROM tbl_tutors");
                while($tutor = $tutors->fetch_assoc()) {
                    echo "<option value='{$tutor['TutorID']}'>{$tutor['TutorName']}</option>";
                }
                ?>
            </select>
            <input type="number" name="credits" placeholder="New Credits" min="1" max="6" required>
            <button type="submit" name="update_unit" class="btn">Update Unit</button>
        </form>

        <!-- Update Student -->
        <h2>👨‍🎓 Update Student</h2>
        <form method="POST" action="update.php">
            <select name="student_id" required>
                <option value="">Select Student to Update</option>
                <?php
                $students = $conn->query("SELECT StudentID, StudentName FROM tbl_students");
                while($student = $students->fetch_assoc()) {
                    echo "<option value='{$student['StudentID']}'>{$student['StudentName']}</option>";
                }
                ?>
            </select>
            <input type="text" name="student_name" placeholder="New Student Name" required>
            <input type="email" name="student_email" placeholder="New Email" required>
            <input type="text" name="phone" placeholder="New Phone" required>
            <button type="submit" name="update_student" class="btn">Update Student</button>
        </form>

        <?php
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Update Tutor
            if (isset($_POST['update_tutor'])) {
                $tutor_id = $_POST['tutor_id'];
                $name = $_POST['tutor_name'];
                $email = $_POST['tutor_email'];
                $department = $_POST['department'];

                $conn->query("UPDATE tbl_tutors SET
                    TutorName = '$name',
                    TutorEmail = '$email',
                    Department = '$department'
                    WHERE TutorID = '$tutor_id'
                ");
                header("Location: update.php");
            }

            // Update Unit
            if (isset($_POST['update_unit'])) {
                $unit_id = $_POST['unit_id'];
                $name = $_POST['unit_name'];
                $tutor_id = $_POST['tutor_id'];
                $credits = $_POST['credits'];

                $conn->query("UPDATE tbl_units SET
                    UnitName = '$name',
                    TutorID = '$tutor_id',
                    Credits = '$credits'
                    WHERE UnitID = '$unit_id'
                ");
                header("Location: update.php");
            }

            // Update Student
            if (isset($_POST['update_student'])) {
                $student_id = $_POST['student_id'];
                $name = $_POST['student_name'];
                $email = $_POST['student_email'];
                $phone = $_POST['phone'];

                $conn->query("UPDATE tbl_students SET
                    StudentName = '$name',
                    StudentEmail = '$email',
                    Phone = '$phone'
                    WHERE StudentID = '$student_id'
                ");
                header("Location: update.php");
            }
        }
        ?>
    </div>
</body>
</html>`,
  },
  deletePage: {
    title: "delete.php - Remove Records",
    description:
      "Simple forms for deleting tutors, units, students, and enrollments",
    language: "php",
    code: `<?php include "config.php"; ?>
<!DOCTYPE html>
<html>
<head>
    <title>Delete Records</title>
    <link rel="stylesheet" href="tutorial-styles.css">
</head>
<body>
    <div class="container">
        <h1>🗑️ Delete Records</h1>
        <a href="index.php" class="btn">← Back to Main</a>

        <!-- Delete Tutor -->
        <h2>👨‍🏫 Delete Tutor</h2>
        <form method="POST" action="delete.php">
            <select name="tutor_id" required>
                <option value="">Select Tutor to Delete</option>
                <?php
                $tutors = $conn->query("SELECT TutorID, TutorName FROM tbl_tutors");
                while($tutor = $tutors->fetch_assoc()) {
                    echo "<option value='{$tutor['TutorID']}'>{$tutor['TutorName']}</option>";
                }
                ?>
            </select>
            <button type="submit" name="delete_tutor" class="btn btn-danger">Delete Tutor</button>
        </form>

        <!-- Delete Unit -->
        <h2>📚 Delete Unit</h2>
        <form method="POST" action="delete.php">
            <select name="unit_id" required>
                <option value="">Select Unit to Delete</option>
                <?php
                $units = $conn->query("SELECT UnitID, UnitName FROM tbl_units");
                while($unit = $units->fetch_assoc()) {
                    echo "<option value='{$unit['UnitID']}'>{$unit['UnitName']}</option>";
                }
                ?>
            </select>
            <button type="submit" name="delete_unit" class="btn btn-danger">Delete Unit</button>
        </form>

        <!-- Delete Student -->
        <h2>👨‍🎓 Delete Student</h2>
        <form method="POST" action="delete.php">
            <select name="student_id" required>
                <option value="">Select Student to Delete</option>
                <?php
                $students = $conn->query("SELECT StudentID, StudentName FROM tbl_students");
                while($student = $students->fetch_assoc()) {
                    echo "<option value='{$student['StudentID']}'>{$student['StudentName']}</option>";
                }
                ?>
            </select>
            <button type="submit" name="delete_student" class="btn btn-danger">Delete Student</button>
        </form>

        <!-- Delete Enrollment -->
        <h2>📝 Delete Enrollment</h2>
        <form method="POST" action="delete.php">
            <select name="enrollment_id" required>
                <option value="">Select Enrollment to Delete</option>
                <?php
                $enrollments = $conn->query("SELECT e.EnrollmentID, s.StudentName, u.UnitName
                                           FROM tbl_enrollments e
                                           JOIN tbl_students s ON e.StudentID = s.StudentID
                                           JOIN tbl_units u ON e.UnitID = u.UnitID");
                while($enrollment = $enrollments->fetch_assoc()) {
                    echo "<option value='{$enrollment['EnrollmentID']}'>{$enrollment['StudentName']} - {$enrollment['UnitName']}</option>";
                }
                ?>
            </select>
            <button type="submit" name="delete_enrollment" class="btn btn-danger">Delete Enrollment</button>
        </form>

        <?php
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Delete Tutor
            if (isset($_POST['delete_tutor'])) {
                $tutor_id = $_POST['tutor_id'];
                $conn->query("DELETE FROM tbl_tutors WHERE TutorID = '$tutor_id'");
                header("Location: delete.php");
            }

            // Delete Unit
            if (isset($_POST['delete_unit'])) {
                $unit_id = $_POST['unit_id'];
                $conn->query("DELETE FROM tbl_units WHERE UnitID = '$unit_id'");
                header("Location: delete.php");
            }

            // Delete Student
            if (isset($_POST['delete_student'])) {
                $student_id = $_POST['student_id'];
                $conn->query("DELETE FROM tbl_students WHERE StudentID = '$student_id'");
                header("Location: delete.php");
            }

            // Delete Enrollment
            if (isset($_POST['delete_enrollment'])) {
                $enrollment_id = $_POST['enrollment_id'];
                $conn->query("DELETE FROM tbl_enrollments WHERE EnrollmentID = '$enrollment_id'");
                header("Location: delete.php");
            }
        }
        ?>
    </div>
</body>
</html>`,
  },
  cssStyles: {
    title: "tutorial-styles.css - Complete CSS for Tutorial Management",
    description: "Professional styling for all tutorial management pages",
    language: "css",
    code: `/* Tutorial Management System Styles */
body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 20px;
    background-color: #f5f5f5;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    background-color: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

h1 {
    color: #2c3e50;
    text-align: center;
    margin-bottom: 30px;
    font-size: 2.5em;
}

h2 {
    color: #34495e;
    border-bottom: 2px solid #3498db;
    padding-bottom: 10px;
    margin-top: 30px;
}

.nav-buttons {
    text-align: center;
    margin-bottom: 30px;
}

.btn {
    display: inline-block;
    padding: 12px 24px;
    margin: 5px;
    background-color: #3498db;
    color: white;
    text-decoration: none;
    border-radius: 5px;
    border: none;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s;
}

.btn:hover {
    background-color: #2980b9;
}

.btn-danger {
    background-color: #e74c3c;
}

.btn-danger:hover {
    background-color: #c0392b;
}

.section {
    margin-bottom: 40px;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background-color: #fafafa;
}

table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 15px;
}

th, td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

th {
    background-color: #3498db;
    color: white;
    font-weight: bold;
}

tr:hover {
    background-color: #f5f5f5;
}

form {
    background-color: white;
    padding: 20px;
    margin: 15px 0;
    border-radius: 8px;
    border: 1px solid #ddd;
}

input[type="text"], input[type="email"], input[type="number"], select {
    width: 100%;
    padding: 10px;
    margin: 8px 0;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-sizing: border-box;
    font-size: 16px;
}

input[type="text"]:focus, input[type="email"]:focus, input[type="number"]:focus, select:focus {
    border-color: #3498db;
    outline: none;
    box-shadow: 0 0 5px rgba(52, 152, 219, 0.3);
}

p {
    color: #7f8c8d;
    font-style: italic;
}

a {
    color: #3498db;
    text-decoration: none;
}

a:hover {
    color: #2980b9;
    text-decoration: underline;
}`,
  },
};
