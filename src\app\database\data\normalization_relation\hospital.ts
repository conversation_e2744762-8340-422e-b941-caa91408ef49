// Hospital Management System Data
export const hospitalData = {
  normalization: [
    {
      title: "📋  The Challenge - Try It Yourself!",
      description: `Here's an unnormalized hospital management table. Can you figure out how to normalize it?
  
  **Original Messy Table:**
   DoctorID | DoctorName | DoctorEmail | DeptID | DeptName | PatientID | PatientName | PatientPhone | AppointmentID | AppointmentDate | TreatmentID | TreatmentName | Cost
  
  
  **Problems to Identify:**
  • What information is being repeated?
  • How many separate tables should this become?
  • What would be the primary keys for each table?
  
  **Your Challenge:** Think about how you would split this into clean, organized tables before looking at the solution!`,
      language: "text",
    },
    {
      title: "💡 UI-Based Solution (phpMyAdmin Method)",
      description: `**Perfect! Here's how to normalize the hospital management database using phpMyAdmin:**
  
  **Step 1: Open phpMyAdmin**
  1. Start XAMPP Control Panel
  2. Click "Start" for Apache and MySQL
  3. Click "Admin" next to MySQL to open phpMyAdmin
  4. Click "New" to create a new database
  5. Name it "hospital_management" and click "Create"
  
  **Step 2: Create Departments Table**
  1. Click on your database name "hospital_management"
  2. Click "Create table"
  3. Table name: "tbl_departments", Number of columns: 3
  4. Set up columns:
     - DeptID: VARCHAR(10), Primary Key
     - DeptName: VARCHAR(255), NOT NULL
     - HeadOfDept: VARCHAR(255)
  
  **Step 3: Create Doctors Table**
  1. Click "Create table" again
  2. Table name: "tbl_doctors", Number of columns: 5
  3. Set up columns:
     - DoctorID: VARCHAR(10), Primary Key
     - DoctorName: VARCHAR(255), NOT NULL
     - DoctorEmail: VARCHAR(255), UNIQUE
     - DeptID: VARCHAR(10), Index
     - Specialization: VARCHAR(255)
  
  **Step 4: Create Patients Table**
  1. Click "Create table" again
  2. Table name: "tbl_patients", Number of columns: 5
  3. Set up columns:
     - PatientID: VARCHAR(10), Primary Key
     - PatientName: VARCHAR(255), NOT NULL
     - PatientPhone: VARCHAR(15)
     - PatientEmail: VARCHAR(255)
     - Address: TEXT
  
  **Step 5: Create Appointments Table**
  1. Click "Create table" again
  2. Table name: "tbl_appointments", Number of columns: 5
  3. Set up columns:
     - AppointmentID: INT, Primary Key, Auto Increment
     - DoctorID: VARCHAR(10), Index
     - PatientID: VARCHAR(10), Index
     - AppointmentDate: DATETIME, NOT NULL
     - Status: VARCHAR(50)
  
  **Step 6: Create Treatments Table**
  1. Click "Create table" again
  2. Table name: "tbl_treatments", Number of columns: 5
  3. Set up columns:
     - TreatmentID: INT, Primary Key, Auto Increment
     - AppointmentID: INT, Index
     - TreatmentName: VARCHAR(255), NOT NULL
     - Cost: DECIMAL(10,2)
     - Notes: TEXT
  
  **Step 7: Set Up Relationships**
  1. Go to tbl_doctors table → Set DeptID to reference tbl_departments(DeptID)
  2. Go to tbl_appointments table → Set DoctorID to reference tbl_doctors(DoctorID)
  3. Go to tbl_appointments table → Set PatientID to reference tbl_patients(PatientID)
  4. Go to tbl_treatments table → Set AppointmentID to reference tbl_appointments(AppointmentID)
  
  ✅ **Done! Your normalized database is ready!**`,
      code: ``,
      language: "text",
    },
    {
      title: "Normalization Guide - SQL Code Method",
      description: `**Complete SQL Code to Create Normalized Hospital Management Database**
  
  Copy and paste this code into phpMyAdmin's SQL tab:`,
      code: `-- Step 1: Create Database
  CREATE DATABASE hospital_management;
  USE hospital_management;
  
  -- Step 2: Create Departments Table
  CREATE TABLE tbl_departments (
      DeptID INT PRIMARY KEY AUTO_INCREMENT,
      DeptName VARCHAR(255) NOT NULL,
      HeadOfDept VARCHAR(255)
  );
  
  -- Step 3: Create Doctors Table
  CREATE TABLE tbl_doctors (
      DoctorID INT PRIMARY KEY AUTO_INCREMENT,
      DoctorName VARCHAR(255) NOT NULL,
      DoctorEmail VARCHAR(255) UNIQUE,
      DeptID VARCHAR(10),
      Specialization VARCHAR(255),
      FOREIGN KEY (DeptID) REFERENCES tbl_departments(DeptID)
  );
  
  -- Step 4: Create Patients Table
  CREATE TABLE tbl_patients (
      PatientID INT PRIMARY KEY AUTO_INCREMENT,
      PatientName VARCHAR(255) NOT NULL,
      PatientPhone VARCHAR(15),
      PatientEmail VARCHAR(255),
      Address TEXT
  );
  
  -- Step 5: Create Appointments Table
  CREATE TABLE tbl_appointments (
      AppointmentID INT PRIMARY KEY AUTO_INCREMENT,
      DoctorID VARCHAR(10),
      PatientID VARCHAR(10),
      AppointmentDate DATETIME NOT NULL,
      Status VARCHAR(50) DEFAULT 'Scheduled',
      FOREIGN KEY (DoctorID) REFERENCES tbl_doctors(DoctorID),
      FOREIGN KEY (PatientID) REFERENCES tbl_patients(PatientID)
  );
  
  -- Step 6: Create Treatments Table
  CREATE TABLE tbl_treatments (
      TreatmentID INT PRIMARY KEY AUTO_INCREMENT,
      AppointmentID INT,
      TreatmentName VARCHAR(255) NOT NULL,
      Cost DECIMAL(10,2),
      Notes TEXT,
      FOREIGN KEY (AppointmentID) REFERENCES tbl_appointments(AppointmentID)
  );
  
  -- Step 7: Insert Sample Data
  INSERT INTO tbl_departments VALUES
  ('DEPT001', 'Cardiology', 'Dr. Heart'),
  ('DEPT002', 'Neurology', 'Dr. Brain'),
  ('DEPT003', 'Pediatrics', 'Dr. Child');
  
  INSERT INTO tbl_doctors VALUES
  ('DOC001', 'Dr. Smith', '<EMAIL>', 'DEPT001', 'Cardiologist'),
  ('DOC002', 'Dr. Johnson', '<EMAIL>', 'DEPT002', 'Neurologist'),
  ('DOC003', 'Dr. Brown', '<EMAIL>', 'DEPT003', 'Pediatrician');
  
  INSERT INTO tbl_patients VALUES
  ('PAT001', 'John Doe', '555-0101', '<EMAIL>', '123 Main St'),
  ('PAT002', 'Jane Smith', '555-0102', '<EMAIL>', '456 Oak Ave'),
  ('PAT003', 'Bob Wilson', '555-0103', '<EMAIL>', '789 Pine Rd');
  
  INSERT INTO tbl_appointments VALUES
  (1, 'DOC001', 'PAT001', '2024-01-15 10:00:00', 'Completed'),
  (2, 'DOC002', 'PAT002', '2024-01-16 14:30:00', 'Scheduled'),
  (3, 'DOC003', 'PAT003', '2024-01-17 09:15:00', 'Completed');
  
  INSERT INTO tbl_treatments VALUES
  (1, 1, 'ECG Test', 150.00, 'Normal heart rhythm'),
  (2, 3, 'Vaccination', 50.00, 'Annual flu shot');`,
      language: "sql",
    },
  ],
  relationships: [
    {
      title: "Understanding Hospital Management Relationships",
      description: `**Why do we need relationships in our hospital management system?**
  
  In a hospital, we have five main entities:
  - **Departments** (medical specialties like Cardiology, Neurology)
  - **Doctors** (medical professionals)
  - **Patients** (people receiving care)
  - **Appointments** (scheduled visits)
  - **Treatments** (medical procedures and services)
  
  **The Relationships:**
  1. **Departments → Doctors**: One department has multiple doctors (1-to-Many)
  2. **Doctors → Appointments**: One doctor can have multiple appointments (1-to-Many)
  3. **Patients → Appointments**: One patient can have multiple appointments (1-to-Many)
  4. **Appointments → Treatments**: One appointment can have multiple treatments (1-to-Many)
  
  This way, we can easily track patient care, doctor schedules, and medical treatments!`,
      code: ``,
      language: "text",
    },
  ],
};
