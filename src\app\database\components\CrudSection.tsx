import { TableScenario } from "../data/tableScenarios";
import { getCrudDataByScenario } from "../data/crud";
import CodeBlock from "./CodeBlock";

// Helper functions for CSS management
function getCSSFileName(scenarioId: string): string {
  switch (scenarioId) {
    case "Beginner crud":
      return "crud-styles.css";
    case "Library management":
      return "library-styles.css";
    case "Student grades":
      return "grades-styles.css";
    default:
      return "universal-crud-styles.css";
  }
}

function getCSSContent(scenarioId: string): string {
  if (scenarioId === "Beginner crud") {
    return `/* Basic CRUD Operations CSS Styles */
/* This file contains all the CSS styling for CRUD operations */
/* Students can link this file to their PHP pages for better presentation */

/* Table Styling */
table {
    width: 80%;
    border-collapse: collapse;
    margin: 20px 0;
    font-family: Arial, sans-serif;
}

table, th, td {
    border: 1px solid #ddd;
}

th, td {
    padding: 12px;
    text-align: left;
}

th {
    background-color: #f2f2f2;
    font-weight: bold;
}

/* Alternating row colors for better readability */
tr:nth-child(even) {
    background-color: #f9f9f9;
}

tr:hover {
    background-color: #f5f5f5;
}

/* Form Styling */
form {
    max-width: 600px;
    margin: 20px 0;
}

input[type="text"],
input[type="email"],
input[type="number"] {
    width: 100%;
    padding: 8px;
    margin: 5px 0 15px 0;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-sizing: border-box;
}

input[type="submit"] {
    background-color: #4CAF50;
    color: white;
    padding: 12px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
}

input[type="submit"]:hover {
    background-color: #45a049;
}

/* Navigation Links */
a {
    color: #007bff;
    text-decoration: none;
    padding: 8px 12px;
    margin: 5px;
    border: 1px solid #007bff;
    border-radius: 4px;
    display: inline-block;
}

a:hover {
    background-color: #007bff;
    color: white;
}

/* Page Headers */
h1 {
    color: #333;
    font-family: Arial, sans-serif;
    margin-bottom: 20px;
}`;
  }

  return `/* Universal CRUD Operations CSS Styles */
/* This file can be used by multiple CRUD scenarios for consistent styling */

/* Base Styles */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

/* Headers */
h1 {
    color: #2c3e50;
    text-align: center;
    margin-bottom: 30px;
    font-size: 2.5em;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

/* Navigation Buttons */
.nav-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin: 30px 0;
    padding: 25px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    flex-wrap: wrap;
}

.btn {
    display: inline-block;
    padding: 12px 24px;
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    color: white;
    text-decoration: none;
    border-radius: 8px;
    border: none;
    cursor: pointer;
    font-size: 16px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.btn:hover {
    background: linear-gradient(135deg, #2980b9 0%, #1f618d 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0,0,0,0.2);
}

/* Tables */
table {
    width: 100%;
    border-collapse: collapse;
    margin: 25px 0;
    background-color: white;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    border-radius: 10px;
    overflow: hidden;
}

th, td {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid #ecf0f1;
}

th {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    color: white;
    font-weight: 600;
}

tr:nth-child(even) {
    background-color: #f8f9fa;
}

tr:hover {
    background-color: #e3f2fd;
    transform: scale(1.01);
    transition: all 0.2s ease;
}

/* Forms */
input[type="text"],
input[type="email"],
input[type="number"],
input[type="date"],
select,
textarea {
    width: 100%;
    padding: 12px 15px;
    margin: 8px 0;
    border: 2px solid #bdc3c7;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="number"]:focus,
input[type="date"]:focus,
select:focus,
textarea:focus {
    border-color: #3498db;
    outline: none;
    box-shadow: 0 0 10px rgba(52, 152, 219, 0.3);
}

input[type="submit"],
button {
    background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
    color: white;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 16px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

input[type="submit"]:hover,
button:hover {
    background: linear-gradient(135deg, #229954 0%, #1e8449 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0,0,0,0.2);
}

/* Links */
a {
    color: #3498db;
    text-decoration: none;
    transition: color 0.3s ease;
}

a:hover {
    color: #2980b9;
    text-decoration: underline;
}`;
}

// CRUD Section Component
export function CrudSection({ scenario }: { scenario: TableScenario }) {
  // Get the appropriate CRUD data based on the selected scenario
  const crudData = getCrudDataByScenario(scenario.id);

  return (
    <div className="space-y-6">
      <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
        CRUD Operations
      </h3>
      <p className="text-gray-600 dark:text-gray-400">
        Complete Create, Read, Update, Delete operations for your{" "}
        {scenario.title.toLowerCase()} database.
      </p>

      {/* Database Setup - Only show for beginner CRUD */}
      {scenario.id === "Beginner crud" && "dbSetup" in crudData && (
        <div className="bg-purple-50 dark:bg-gray-700 border border-purple-200 dark:border-purple-600 p-6 rounded-lg">
          <h3 className="text-lg font-semibold mb-4 text-purple-800 dark:text-purple-200">
            🗄️ Step 1: Database Setup (SQL Commands)
          </h3>
          <p className="text-purple-700 dark:text-purple-300 mb-4">
            First, create your database and table structure in phpMyAdmin:
          </p>
          <CodeBlock
            title={
              (
                crudData as {
                  dbSetup: {
                    title: string;
                    description: string;
                    language: string;
                    code: string;
                  };
                }
              ).dbSetup.title
            }
            description={
              (
                crudData as {
                  dbSetup: {
                    title: string;
                    description: string;
                    language: string;
                    code: string;
                  };
                }
              ).dbSetup.description
            }
            language={
              (
                crudData as {
                  dbSetup: {
                    title: string;
                    description: string;
                    language: string;
                    code: string;
                  };
                }
              ).dbSetup.language
            }
            code={
              (
                crudData as {
                  dbSetup: {
                    title: string;
                    description: string;
                    language: string;
                    code: string;
                  };
                }
              ).dbSetup.code || ""
            }
          />
        </div>
      )}

      {/* Database Connection */}
      <div className="bg-blue-50 dark:bg-gray-700 border border-blue-200 dark:border-blue-600 p-6 rounded-lg">
        <h3 className="text-lg font-semibold mb-4 text-blue-800 dark:text-blue-200">
          🔗 Step {scenario.id === "Beginner crud" ? "2" : "1"}: Database
          Connection (config.php)
        </h3>
        <p className="text-blue-700 dark:text-blue-300 mb-4">
          {scenario.id === "Beginner crud"
            ? "Now create the database connection file that all other files will use:"
            : "First, create the database connection file that all other files will use:"}
        </p>
        <CodeBlock
          title={crudData.dbConnect.title}
          description={crudData.dbConnect.description}
          language={crudData.dbConnect.language}
          code={crudData.dbConnect.code || ""}
        />
      </div>

      {/* Homepage */}
      <div className="bg-green-50 dark:bg-gray-700 border border-green-200 dark:border-green-600 p-6 rounded-lg">
        <h3 className="text-lg font-semibold mb-4 text-green-800 dark:text-green-200">
          🏠 Step {scenario.id === "Beginner crud" ? "3" : "2"}: Homepage & Data
          Viewer (index.php)
        </h3>
        <p className="text-green-700 dark:text-green-300 mb-4">
          This is your main dashboard that provides navigation to all CRUD
          operations AND displays all data from your tables:
        </p>
        <CodeBlock
          title={crudData.indexPage.title}
          description={crudData.indexPage.description}
          language={crudData.indexPage.language}
          code={crudData.indexPage.code || ""}
        />
      </div>

      {/* CREATE Operation */}
      <div className="bg-orange-50 dark:bg-gray-700 border border-orange-200 dark:border-orange-600 p-6 rounded-lg">
        <h3 className="text-lg font-semibold mb-4 text-orange-800 dark:text-orange-200">
          ➕ Step {scenario.id === "Beginner crud" ? "4" : "3"}: CREATE
          Operations (create.php)
        </h3>
        <p className="text-orange-700 dark:text-orange-300 mb-4">
          This file provides forms to add new records to all tables:
        </p>
        <CodeBlock
          title={crudData.createPage.title}
          description={crudData.createPage.description}
          language={crudData.createPage.language}
          code={crudData.createPage.code || ""}
        />
      </div>

      {/* UPDATE Operation */}
      <div className="bg-yellow-50 dark:bg-gray-700 border border-yellow-200 dark:border-yellow-600 p-6 rounded-lg">
        <h3 className="text-lg font-semibold mb-4 text-yellow-800 dark:text-yellow-200">
          ✏️ Step {scenario.id === "Beginner crud" ? "5" : "4"}: UPDATE
          Operations (update.php)
        </h3>
        <p className="text-yellow-700 dark:text-yellow-300 mb-4">
          This file handles editing existing records with pre-filled forms:
        </p>
        <CodeBlock
          title={crudData.updatePage.title}
          description={crudData.updatePage.description}
          language={crudData.updatePage.language}
          code={crudData.updatePage.code || ""}
        />
      </div>

      {/* DELETE Operation */}
      <div className="bg-red-50 dark:bg-gray-700 border border-red-200 dark:border-red-600 p-6 rounded-lg">
        <h3 className="text-lg font-semibold mb-4 text-red-800 dark:text-red-200">
          🗑️ Step {scenario.id === "Beginner crud" ? "6" : "5"}: DELETE
          Operations (delete.php)
        </h3>
        <p className="text-red-700 dark:text-red-300 mb-4">
          This file handles safe deletion of records with dependency checking:
        </p>
        <CodeBlock
          title={crudData.deletePage.title}
          description={crudData.deletePage.description}
          language={crudData.deletePage.language}
          code={crudData.deletePage.code || ""}
        />
      </div>

      {/* CSS Styling Reference */}
      <div className="bg-indigo-50 dark:bg-gray-700 border border-indigo-200 dark:border-indigo-600 p-6 rounded-lg">
        <h3 className="text-lg font-semibold mb-4 text-indigo-800 dark:text-indigo-200">
          🎨 Step {scenario.id === "Beginner crud" ? "7" : "6"}: CSS Styling (
          {getCSSFileName(scenario.id)}) - Optional
        </h3>
        <p className="text-indigo-700 dark:text-indigo-300 mb-4">
          Create this CSS file to make your CRUD pages look professional. Link
          it in your HTML head section:
        </p>
        <CodeBlock
          title={`${getCSSFileName(
            scenario.id
          )} - Complete CSS for CRUD Operations`}
          description={`Save this as '${getCSSFileName(
            scenario.id
          )}' in the same folder as your PHP files. This separates styling from functionality, making your code cleaner and easier to maintain.`}
          language="css"
          code={getCSSContent(scenario.id)}
        />

        <div className="mt-4 p-4 bg-indigo-100 dark:bg-gray-600 rounded-lg">
          <h4 className="text-sm font-semibold text-indigo-800 dark:text-indigo-200 mb-2">
            💡 How to use this CSS:
          </h4>
          <ol className="text-sm text-indigo-700 dark:text-indigo-300 space-y-1">
            <li>
              1. Copy the CSS code above and save it as{" "}
              <code className="bg-white dark:bg-gray-800 px-1 rounded">
                {getCSSFileName(scenario.id)}
              </code>
            </li>
            <li>2. Place the CSS file in the same folder as your PHP files</li>
            <li>
              3. Add{" "}
              <code className="bg-white dark:bg-gray-800 px-1 rounded">
                &lt;link rel=&quot;stylesheet&quot; href=&quot;
                {getCSSFileName(scenario.id)}&quot;&gt;
              </code>{" "}
              in your HTML head section
            </li>
            <li>4. Your pages will now have professional styling!</li>
          </ol>
        </div>
      </div>
    </div>
  );
}
