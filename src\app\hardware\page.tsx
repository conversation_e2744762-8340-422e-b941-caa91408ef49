"use client";

import { useState } from "react";
import { Search, X } from "lucide-react";

interface HardwareComponent {
  id: string;
  name: string;
  image: string; // can be emoji or image path
  description: string;
  features: string[];
  variation?: string[];
  // Optional fields for legacy support
  category?: string;
  type?: string;
  specifications?: Record<string, string>;
  commonUses?: string[];
  connectionType?: string;
  formFactor?: string;
  yearIntroduced?: string;
  isLegacy?: boolean;
}

const hardwareData: HardwareComponent[] = [
  // Processors
  {
    id: "1",
    name: "Central Processing Unit (CPU)",
    category: "Processors",
    type: "Main Processor",
    description:
      "The brain of the computer that executes instructions and performs calculations",
    features: [
      "Instruction execution",
      "Arithmetic operations",
      "Cache memory",
      "Multiple cores",
    ],
    specifications: {
      "Socket Types": "LGA, PGA, BGA",
      Architecture: "64-bit x86, ARM",
      "Cache Levels": "L1, L2, L3",
      "Typical Cores": "2-32 cores",
    },
    image: "/image/cpu.png",
    commonUses: [
      "General computing",
      "Gaming",
      "Server applications",
      "Workstations",
    ],
    connectionType: "CPU Socket",
    yearIntroduced: "1971",
  },
  {
    id: "2",
    name: "Graphics Processing Unit (GPU)",
    category: "Processors",
    type: "Graphics Processor",
    description:
      "Specialized processor for rendering graphics and parallel computing tasks",
    features: [
      "3D rendering",
      "Video encoding/decoding",
      "Parallel processing",
      "AI acceleration",
    ],
    specifications: {
      Interface: "PCIe x16, AGP (legacy)",
      "Memory Type": "GDDR6/6X, HBM",
      "Output Ports": "HDMI, DisplayPort, DVI",
      "Power Range": "75W-450W",
    },
    image: "/image/gpu.png",
    commonUses: ["Gaming", "3D modeling", "Video editing", "Machine learning"],
    connectionType: "PCIe x16 slot",
  },

  // Memory Types
  {
    id: "3",
    name: "DDR4 SDRAM",
    category: "Memory",
    type: "System Memory",
    description: "Fourth generation Double Data Rate synchronous dynamic RAM",
    features: [
      "Volatile storage",
      "High bandwidth",
      "Low latency",
      "ECC variants available",
    ],
    specifications: {
      "Speed Range": "2133-3200 MHz",
      Voltage: "1.2V",
      "Pin Count": "288-pin DIMM, 260-pin SO-DIMM",
      "Capacity Range": "4GB-128GB per module",
    },
    image: "/image/sdram.jpg",
    commonUses: ["System memory", "Gaming", "Workstations", "Servers"],
    connectionType: "DIMM/SO-DIMM slot",
    formFactor: "DIMM, SO-DIMM",
    yearIntroduced: "2014",
  },
  {
    id: "4",
    name: "DDR5 SDRAM",
    category: "Memory",
    type: "Next-Gen Memory",
    description:
      "Fifth generation DDR memory with improved performance and efficiency",
    features: [
      "Higher bandwidth",
      "Lower power",
      "On-die ECC",
      "Dual-channel per DIMM",
    ],
    specifications: {
      "Speed Range": "4800-8400+ MHz",
      Voltage: "1.1V",
      "Pin Count": "288-pin DIMM, 262-pin SO-DIMM",
      "Capacity Range": "8GB-128GB per module",
    },
    image: "/image/dram.webp",
    commonUses: [
      "High-performance computing",
      "Gaming",
      "Content creation",
      "AI workloads",
    ],
    connectionType: "DIMM/SO-DIMM slot",
    formFactor: "DIMM, SO-DIMM",
    yearIntroduced: "2020",
  },

  // Storage - SATA
  {
    id: "5",
    name: "SATA Hard Disk Drive (HDD)",
    category: "Storage",
    type: "Mechanical Storage",
    description: "Traditional spinning disk storage using Serial ATA interface",
    features: [
      "Large capacity",
      "Cost effective",
      "Magnetic storage",
      "Non-volatile",
    ],
    specifications: {
      Interface: "SATA I/II/III (1.5/3/6 Gbps)",
      RPM: "5400, 7200, 10000 RPM",
      Cache: "32MB-256MB",
      Capacity: "500GB-20TB",
    },
    image: "/image/sata-hdd.png",
    commonUses: ["Mass storage", "Backup", "Archive", "Budget systems"],
    connectionType: "SATA",
    formFactor: '3.5", 2.5"',
    yearIntroduced: "2003",
  },
  {
    id: "6",
    name: "SATA Solid State Drive (SSD)",
    category: "Storage",
    type: "Flash Storage",
    description:
      "Flash-based storage using SATA interface for broad compatibility",
    features: [
      "No moving parts",
      "Fast access",
      "Silent operation",
      "Shock resistant",
    ],
    specifications: {
      Interface: "SATA III (6 Gbps)",
      "Read Speed": "500-550 MB/s",
      "Write Speed": "450-520 MB/s",
      Capacity: "120GB-8TB",
    },
    image: "/image/sata-ssd.png",
    commonUses: [
      "OS drive",
      "Application storage",
      "Gaming",
      "Laptop upgrades",
    ],
    connectionType: "SATA",
    formFactor: '2.5", M.2 SATA',
    yearIntroduced: "2009",
  },

  // Storage - Legacy PATA
  {
    id: "7",
    name: "PATA/IDE Hard Drive",
    category: "Storage",
    type: "Legacy Storage",
    description:
      "Parallel ATA interface drives using ribbon cables (also called IDE)",
    features: [
      "Parallel data transfer",
      "Master/slave configuration",
      "Ribbon cable",
      "Legacy compatibility",
    ],
    specifications: {
      Interface: "PATA/IDE (133 MB/s max)",
      "Cable Type": "40/80-pin ribbon cable",
      "Devices per cable": "2 (Master/Slave)",
      Capacity: "40MB-500GB typical",
    },
    image: "/image/pata-ide.png",
    commonUses: [
      "Older systems",
      "Retro computing",
      "Legacy support",
      "Data recovery",
    ],
    connectionType: "PATA/IDE",
    formFactor: '3.5", 2.5"',
    yearIntroduced: "1986",
    isLegacy: true,
  },

  // Storage - NVMe
  {
    id: "8",
    name: "NVMe M.2 SSD",
    category: "Storage",
    type: "High-Speed Storage",
    description: "Ultra-fast storage using PCIe interface with NVMe protocol",
    features: [
      "PCIe interface",
      "NVMe protocol",
      "Compact M.2 form factor",
      "Very high performance",
    ],
    specifications: {
      Interface: "PCIe 3.0/4.0/5.0 x4",
      "Read Speed": "3000-7000+ MB/s",
      "Write Speed": "2000-6000+ MB/s",
      "Form Factors": "M.2 2280, 2242, 2260, 22110",
    },
    image: "/image/nvme-m2.png",
    commonUses: [
      "High-performance computing",
      "Gaming",
      "Video editing",
      "Workstations",
    ],
    connectionType: "M.2 PCIe",
    formFactor: "M.2",
    yearIntroduced: "2013",
  },

  // Example new items:
  {
    id: "100",
    name: "Resistor",
    image: "/image/resistor.png",
    description:
      "A passive electronic component that resists the flow of electric current.",
    features: [
      "Color-coded bands",
      "Limits current",
      "Used in voltage dividers",
    ],
  },
  {
    id: "101",
    name: "Capacitor",
    image: "/image/capacitor.png",
    description: "Stores electrical energy temporarily in an electric field.",
    features: [
      "Polarized/non-polarized",
      "Used for filtering",
      "Energy storage",
    ],
  },
  {
    id: "102",
    name: "Breadboard",
    image: "/image/breadboard.png",
    description:
      "A tool for prototyping electronic circuits without soldering.",
    features: ["Reusable", "No soldering required", "For prototyping"],
  },
  {
    id: "103",
    name: "Microcontroller (Arduino Uno)",
    image: "/image/arduino-uno.png",
    description:
      "A popular microcontroller board for learning and prototyping.",
    features: ["ATmega328P", "Digital/analog I/O", "USB programmable"],
  },
  {
    id: "104",
    name: "USB Flash Drive",
    image: "/image/usb-flash-drive.png",
    description:
      "Portable storage device using flash memory and USB interface.",
    features: ["Portable", "Plug-and-play", "Non-volatile storage"],
  },
  {
    id: "105",
    name: "HDMI Cable",
    image: "/image/hdmi-cable.png",
    description: "Cable for transmitting high-definition video and audio.",
    features: ["High bandwidth", "Audio/video", "Digital signal"],
  },
  {
    id: "106",
    name: "Raspberry Pi",
    image: "/image/raspberry-pi.png",
    description:
      "A small, affordable single-board computer for learning and projects.",
    features: ["ARM CPU", "GPIO pins", "Runs Linux"],
  },
  {
    id: "107",
    name: "VGA Cable",
    image: "/image/vga-cable.png",
    description: "Analog cable for connecting monitors and projectors.",
    features: ["15-pin connector", "Analog video", "Legacy display"],
  },
  {
    id: "108",
    name: "Multimeter",
    image: "/image/multimeter.png",
    description: "Instrument for measuring voltage, current, and resistance.",
    features: ["Measures V/A/Ω", "Digital/analog", "Essential for electronics"],
  },
];

const categories = ["All", "Processors", "Memory", "Storage", "Motherboards"];

export default function HardwarePage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("All");
  const [showLegacy, setShowLegacy] = useState(true);
  const [selectedComponent, setSelectedComponent] =
    useState<HardwareComponent | null>(null);

  // Filter hardware components
  const filteredHardware = hardwareData.filter((item) => {
    const matchesSearch =
      item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.type?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.category?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory =
      selectedCategory === "All" || item.category === selectedCategory;
    const matchesLegacy = showLegacy || !item.isLegacy;

    return matchesSearch && matchesCategory && matchesLegacy;
  });

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold text-gray-900 dark:text-white">
          Hardware Component Gallery
        </h1>
        <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
          Educational gallery of computer hardware components to help students
          identify and understand different types of computer parts, their
          specifications, and connection types.
        </p>
      </div>

      {/* Search and Filters */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <div className="grid md:grid-cols-3 gap-4 mb-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search components..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              suppressHydrationWarning
            />
          </div>

          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            suppressHydrationWarning
          >
            {categories.map((category) => (
              <option key={category} value={category}>
                {category}
              </option>
            ))}
          </select>

          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={showLegacy}
              onChange={(e) => setShowLegacy(e.target.checked)}
              className="rounded border-gray-300 dark:border-gray-600"
              suppressHydrationWarning
            />
            <span className="text-gray-700 dark:text-gray-300">
              Show Legacy Components
            </span>
          </label>
        </div>

        <div className="text-sm text-gray-600 dark:text-gray-400">
          Found {filteredHardware.length} components
        </div>
      </div>

      {/* Hardware Grid */}
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredHardware.map((item) => (
          <HardwareCard key={item.id} item={item} />
        ))}
      </div>

      {filteredHardware.length === 0 && (
        <div className="text-center py-12">
          <img
            src="/image/fallback.png"
            alt="No components"
            className="h-12 w-12 object-contain mx-auto mb-4"
          />
          <p className="text-gray-500 dark:text-gray-400">
            No components found matching your criteria.
          </p>
        </div>
      )}

      {/* Component Detail Modal */}
      {selectedComponent && (
        <ComponentDetailModal
          component={selectedComponent}
          onClose={() => setSelectedComponent(null)}
        />
      )}
    </div>
  );
}

// Hardware Card Component
function HardwareCard({ item }: { item: HardwareComponent }) {
  const [flipped, setFlipped] = useState(false);
  const handleClick = () => {
    setFlipped(!flipped);
  };
  return (
    <div
      className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-600 transition-all cursor-pointer h-96 flex flex-col items-center justify-center p-4"
      onClick={handleClick}
    >
      {!flipped ? (
        <div className="text-center">
          {item.image.startsWith("/image/") ? (
            <img
              src={item.image}
              alt={item.name}
              className="h-64 w-64 object-contain mx-auto"
            />
          ) : (
            <div className="text-6xl mx-auto">{item.image}</div>
          )}
          <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
            Click to see details
          </p>
        </div>
      ) : (
        <div className="flex flex-col items-center w-full max-w-xs mx-auto px-2 py-2 text-center">
          {item.image.startsWith("/image/") ? (
            <img
              src={item.image}
              alt={item.name}
              className="h-48 w-48 object-contain mx-auto mb-2"
            />
          ) : (
            <div className="text-6xl mx-auto mb-2">{item.image}</div>
          )}
          <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2 break-words">
            {item.name}
          </h3>
          <div className="w-full">
            <h4 className="font-semibold text-gray-900 dark:text-white mb-1 text-left">
              Key Features
            </h4>
            <ul className="list-disc list-inside text-left text-gray-700 dark:text-gray-200 mb-2">
              {item.features.map((feature, index) => (
                <li key={index}>{feature}</li>
              ))}
            </ul>
            {item.variation && item.variation.length > 0 && (
              <>
                <div className="border-t border-gray-300 dark:border-gray-600 my-2"></div>
                <h4 className="font-semibold text-gray-900 dark:text-white mb-1 text-left">
                  Variations
                </h4>
                <ul className="list-disc list-inside text-left text-gray-700 dark:text-gray-200">
                  {item.variation.map((v, i) => (
                    <li key={i}>{v}</li>
                  ))}
                </ul>
              </>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

// Component Detail Modal
function ComponentDetailModal({
  component,
  onClose,
}: {
  component: HardwareComponent;
  onClose: () => void;
}) {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-auto">
        <div className="p-6 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between">
          <h2 className="text-2xl font-semibold text-gray-900 dark:text-white">
            {component.name}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            suppressHydrationWarning
          >
            <X className="h-6 w-6" />
          </button>
        </div>
        <div className="p-6 flex flex-col items-center w-full max-w-md mx-auto">
          {component.image.startsWith("/image/") ? (
            <img
              src={component.image}
              alt={component.name}
              className="h-64 w-64 object-contain mb-2"
            />
          ) : (
            <div className="text-6xl mb-2">{component.image}</div>
          )}
          <h3 className="mt-2 text-lg font-semibold text-gray-900 dark:text-white">
            Key Features
          </h3>
          <ul className="mt-2 list-disc list-inside text-left w-full text-gray-700 dark:text-gray-200 mb-2">
            {component.features.map((feature, index) => (
              <li key={index}>{feature}</li>
            ))}
          </ul>
          {component.variation && component.variation.length > 0 && (
            <>
              <div className="border-t border-gray-300 dark:border-gray-600 my-2 w-full"></div>
              <h4 className="font-semibold text-gray-900 dark:text-white mb-1 text-left w-full">
                Variations
              </h4>
              <ul className="list-disc list-inside text-left w-full text-gray-700 dark:text-gray-200">
                {component.variation.map((v, i) => (
                  <li key={i}>{v}</li>
                ))}
              </ul>
            </>
          )}
        </div>
      </div>
    </div>
  );
}
