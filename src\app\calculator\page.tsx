import Link from "next/link";
import { Calculator, Network, BookOpen, ArrowRight, Brain } from "lucide-react";

export default function CalculatorPage() {
  const calculatorSections = [
    {
      title: "Subnet Calculator",
      description:
        "Calculate subnet information, network addresses, and host ranges with detailed binary representations.",
      icon: Calculator,
      href: "/calculator/subnet",
      color: "blue",
      features: [
        "Network & Broadcast addresses",
        "Host range calculation",
        "Binary representations",
        "Network class detection",
      ],
    },
    {
      title: "VLSM Calculator",
      description:
        "Variable Length Subnet Masking calculator for efficient IP address allocation based on host requirements.",
      icon: Network,
      href: "/calculator/vlsm",
      color: "orange",
      features: [
        "Efficient IP allocation",
        "Host requirement based",
        "Automatic subnet sizing",
        "Detailed subnet breakdown",
      ],
    },
    {
      title: "Subnetting Guide",
      description:
        "Comprehensive numerical guide to subnetting with step-by-step examples and practical exercises.",
      icon: BookOpen,
      href: "/calculator/guide",
      color: "green",
      features: [
        "Step-by-step examples",
        "Numerical calculations",
        "Practice exercises",
        "Theory explanations",
      ],
    },
    {
      title: "Practice Quiz",
      description:
        "Interactive subnet practice with randomly generated problems. Test your skills and get instant feedback.",
      icon: Brain,
      href: "/calculator/practice",
      color: "purple",
      features: [
        "Random subnet problems",
        "Instant answer validation",
        "Score tracking",
        "Progressive difficulty",
      ],
    },
  ];

  const getColorClasses = (color: string) => {
    const colors = {
      blue: {
        bg: "bg-blue-50 dark:bg-blue-900/20",
        border: "border-blue-200 dark:border-blue-600",
        icon: "text-blue-600 dark:text-blue-400",
        title: "text-blue-900 dark:text-blue-100",
        button: "bg-blue-600 hover:bg-blue-700 text-white",
      },
      orange: {
        bg: "bg-orange-50 dark:bg-orange-900/20",
        border: "border-orange-200 dark:border-orange-600",
        icon: "text-orange-600 dark:text-orange-400",
        title: "text-orange-900 dark:text-orange-100",
        button: "bg-orange-600 hover:bg-orange-700 text-white",
      },
      green: {
        bg: "bg-green-50 dark:bg-green-900/20",
        border: "border-green-200 dark:border-green-600",
        icon: "text-green-600 dark:text-green-400",
        title: "text-green-900 dark:text-green-100",
        button: "bg-green-600 hover:bg-green-700 text-white",
      },
      purple: {
        bg: "bg-purple-50 dark:bg-purple-900/20",
        border: "border-purple-200 dark:border-purple-600",
        icon: "text-purple-600 dark:text-purple-400",
        title: "text-purple-900 dark:text-purple-100",
        button: "bg-purple-600 hover:bg-purple-700 text-white",
      },
    };
    return colors[color as keyof typeof colors] || colors.blue;
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold text-gray-900 dark:text-white">
          Subnetting Calculator Hub
        </h1>
        <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
          Comprehensive subnetting tools and guides for network planning, IP
          allocation, and subnet calculations.
        </p>
      </div>

      {/* Calculator Sections */}
      <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
        {calculatorSections.map((section, index) => {
          const colors = getColorClasses(section.color);
          const IconComponent = section.icon;

          return (
            <div
              key={index}
              className={`${colors.bg} ${colors.border} border rounded-lg p-6 transition-all duration-200 hover:shadow-lg`}
            >
              <div className="flex items-center mb-4">
                <IconComponent className={`h-8 w-8 ${colors.icon} mr-3`} />
                <h3 className={`text-xl font-semibold ${colors.title}`}>
                  {section.title}
                </h3>
              </div>

              <p className="text-gray-600 dark:text-gray-300 mb-4">
                {section.description}
              </p>

              <ul className="space-y-2 mb-6">
                {section.features.map((feature, featureIndex) => (
                  <li
                    key={featureIndex}
                    className="flex items-center text-sm text-gray-600 dark:text-gray-400"
                  >
                    <div
                      className={`w-1.5 h-1.5 rounded-full ${colors.icon.replace(
                        "text-",
                        "bg-"
                      )} mr-2`}
                    ></div>
                    {feature}
                  </li>
                ))}
              </ul>

              <Link
                href={section.href}
                className={`inline-flex items-center px-4 py-2 rounded-md ${colors.button} transition-colors duration-200`}
              >
                Open Calculator
                <ArrowRight className="h-4 w-4 ml-2" />
              </Link>
            </div>
          );
        })}
      </div>

      {/* Quick Overview */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
          What You&apos;ll Learn
        </h2>
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="text-center p-4">
            <div className="text-2xl font-bold text-blue-600 dark:text-blue-400 mb-2">
              IP Addressing
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Master IPv4 addressing and network classes
            </p>
          </div>
          <div className="text-center p-4">
            <div className="text-2xl font-bold text-orange-600 dark:text-orange-400 mb-2">
              Subnetting
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Learn to divide networks efficiently
            </p>
          </div>
          <div className="text-center p-4">
            <div className="text-2xl font-bold text-green-600 dark:text-green-400 mb-2">
              VLSM
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Variable Length Subnet Masking techniques
            </p>
          </div>
          <div className="text-center p-4">
            <div className="text-2xl font-bold text-purple-600 dark:text-purple-400 mb-2">
              Binary Math
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Understand binary calculations
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
