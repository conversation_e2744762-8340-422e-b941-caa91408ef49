// Types
export interface TableScenario {
  id: string;
  title: string;
  description: string;
  difficulty: "Beginner" | "Intermediate" | "Advanced";
  difficultyColor: "green" | "yellow" | "red";
  tables: string[];
  icon: string;
  features: string[];
}
// Table scenarios with difficulty levels
export const tableScenarios: TableScenario[] = [
  {
    id: "Beginner crud",
    title: "Learn Basic of CRUD",
    description:
      "Simple introduction to CRUD operations with a basic users table. Perfect for beginners who want to focus only on Create, Read, Update, Delete operations.",
    difficulty: "Beginner",
    difficultyColor: "green",
    tables: ["users"],
    icon: "�",
    features: [
      "Basic CRUD operations only",
      "Simple users table",
      "No normalization complexity",
      "Perfect for beginners",
    ],
  },
  {
    id: "library",
    title: "Library Management System",
    description: "Simple book borrowing system with basic relationships",
    difficulty: "Beginner",
    difficultyColor: "green",
    tables: ["books", "members", "borrowing"],
    icon: "📚",
    features: [
      "Basic 1-to-Many relationships",
      "Simple CRUD operations",
      "Easy to understand",
    ],
  },
  {
    id: "grades",
    title: "Student Grades System",
    description: "Academic grade tracking with students and subjects",
    difficulty: "Beginner",
    difficultyColor: "green",
    tables: ["students", "subjects", "grades"],
    icon: "📝",
    features: [
      "Student-Subject relationships",
      "Grade management",
      "Academic tracking",
    ],
  },
  {
    id: "tutorial",
    title: "Tutorial Management System",
    description: "Educational tutorial system with tutors and enrollments",
    difficulty: "Intermediate",
    difficultyColor: "yellow",
    tables: ["units", "tutors", "students", "enrollment"],
    icon: "🎓",
    features: [
      "Multiple relationships",
      "Complex queries",
      "Real-world scenario",
    ],
  },
  {
    id: "ecommerce",
    title: "E-commerce System",
    description: "Online store with products, customers, and orders",
    difficulty: "Intermediate",
    difficultyColor: "yellow",
    tables: ["products", "customers", "orders", "order_items"],
    icon: "🛒",
    features: ["Business logic", "Order management", "Inventory tracking"],
  },
  {
    id: "university",
    title: "University Management",
    description: "Complex university system with multiple departments",
    difficulty: "Advanced",
    difficultyColor: "red",
    tables: ["colleges", "departments", "faculty", "students", "courses"],
    icon: "🏛️",
    features: [
      "Many-to-Many relationships",
      "Complex normalization",
      "Advanced queries",
    ],
  },
  {
    id: "hospital",
    title: "Hospital Management System",
    description: "Medical system with doctors, patients, and treatments",
    difficulty: "Advanced",
    difficultyColor: "red",
    tables: [
      "doctors",
      "patients",
      "appointments",
      "treatments",
      "departments",
    ],
    icon: "🏥",
    features: [
      "Medical complexity",
      "Multiple constraints",
      "Advanced relationships",
    ],
  },
];
