/* Student Grades System CSS Styles */
/* This file contains all the CSS styling for the grades management CRUD operations */

/* Container and Layout */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    font-family: Arial, sans-serif;
    background-color: #f8f9fa;
}

/* Headers */
h1 {
    color: #2c3e50;
    text-align: center;
    margin-bottom: 30px;
    font-size: 2.5em;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

h2 {
    color: #34495e;
    border-bottom: 3px solid #e74c3c;
    padding-bottom: 10px;
    margin: 30px 0 20px 0;
}

/* Navigation Buttons */
.nav-buttons {
    text-align: center;
    margin: 30px 0;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.btn {
    display: inline-block;
    padding: 12px 24px;
    margin: 5px 10px;
    background-color: #e74c3c;
    color: white;
    text-decoration: none;
    border-radius: 8px;
    border: none;
    cursor: pointer;
    font-size: 16px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

.btn:hover {
    background-color: #c0392b;
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0,0,0,0.3);
}

/* Tables */
table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;
    background-color: white;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    border-radius: 8px;
    overflow: hidden;
}

th, td {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

th {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    color: white;
    font-weight: bold;
    text-transform: uppercase;
    font-size: 0.9em;
    letter-spacing: 1px;
}

tr:nth-child(even) {
    background-color: #f8f9fa;
}

tr:hover {
    background-color: #e8f4f8;
    transform: scale(1.01);
    transition: all 0.2s ease;
}

/* Grade-specific styling */
.grade-a {
    background-color: #d4edda !important;
    color: #155724;
    font-weight: bold;
}

.grade-b {
    background-color: #fff3cd !important;
    color: #856404;
    font-weight: bold;
}

.grade-c {
    background-color: #f8d7da !important;
    color: #721c24;
    font-weight: bold;
}

/* Forms */
.form-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 25px;
    margin: 20px 0;
    border-radius: 12px;
    border-left: 5px solid #e74c3c;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

form {
    margin: 15px 0;
}

input[type="text"], 
input[type="email"], 
input[type="number"],
input[type="date"],
select,
textarea {
    width: 100%;
    padding: 12px;
    margin: 8px 0;
    border: 2px solid #ddd;
    border-radius: 6px;
    box-sizing: border-box;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="number"]:focus,
input[type="date"]:focus,
select:focus,
textarea:focus {
    border-color: #e74c3c;
    outline: none;
    box-shadow: 0 0 5px rgba(231, 76, 60, 0.3);
}

input[type="submit"],
button {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    color: white;
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 16px;
    margin: 10px 5px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

input[type="submit"]:hover,
button:hover {
    background: linear-gradient(135deg, #c0392b 0%, #a93226 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
}

/* Statistics Cards */
.stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin: 30px 0;
}

.stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 25px;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 6px 12px rgba(0,0,0,0.15);
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-card h3 {
    margin: 0 0 10px 0;
    font-size: 2.5em;
    font-weight: bold;
}

.stat-card p {
    margin: 0;
    font-size: 1.2em;
    opacity: 0.9;
}

/* Record Display */
.record {
    background-color: white;
    padding: 20px;
    margin: 15px 0;
    border-radius: 8px;
    border-left: 4px solid #e74c3c;
    box-shadow: 0 2px 6px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.record:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    transform: translateX(5px);
}

/* Success/Error Messages */
.success-message {
    background-color: #d4edda;
    color: #155724;
    padding: 15px;
    border: 1px solid #c3e6cb;
    border-radius: 6px;
    margin: 15px 0;
    border-left: 4px solid #28a745;
}

.error-message {
    background-color: #f8d7da;
    color: #721c24;
    padding: 15px;
    border: 1px solid #f5c6cb;
    border-radius: 6px;
    margin: 15px 0;
    border-left: 4px solid #dc3545;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .nav-buttons .btn {
        display: block;
        margin: 10px 0;
    }
    
    table {
        font-size: 12px;
    }
    
    th, td {
        padding: 8px;
    }
    
    .stats {
        grid-template-columns: 1fr;
    }
    
    h1 {
        font-size: 2em;
    }
}

/* Links */
a {
    color: #e74c3c;
    text-decoration: none;
    transition: color 0.3s ease;
}

a:hover {
    color: #c0392b;
    text-decoration: underline;
}
