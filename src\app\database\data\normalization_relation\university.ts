// University Management System Data
export const universityData = {
  normalization: [
    {
      title: "📋  The Challenge - Try It Yourself!",
      description: `Here's an unnormalized university management table. Can you figure out how to normalize it?
  
  **Original Messy Table:**
   CollegeID | CollegeName | DeptID | DeptName | FacultyID | FacultyName | FacultyEmail | StudentID | StudentName | CourseID | CourseName | Credits
  
  
  **Problems to Identify:**
  • What information is being repeated?
  • How many separate tables should this become?
  • What would be the primary keys for each table?
  
  **Your Challenge:** Think about how you would split this into clean, organized tables before looking at the solution!`,
      language: "text",
    },
    {
      title: "💡 UI-Based Solution (phpMyAdmin Method)",
      description: `**Perfect! Here's how to normalize the university management database using phpMyAdmin:**
  
  **Step 1: Open phpMyAdmin**
  1. Start XAMPP Control Panel
  2. Click "Start" for Apache and MySQL
  3. Click "Admin" next to MySQL to open phpMyAdmin
  4. Click "New" to create a new database
  5. Name it "university_management" and click "Create"
  
  **Step 2: Create Colleges Table**
  1. Click on your database name "university_management"
  2. Click "Create table"
  3. Table name: "tbl_colleges", Number of columns: 3
  4. Set up columns:
     - CollegeID: INT, Primary Key, Auto Increment
     - CollegeName: VARCHAR(255), NOT NULL
     - Dean: VARCHAR(255)

  **Step 3: Create Departments Table**
  1. Click "Create table" again
  2. Table name: "tbl_departments", Number of columns: 4
  3. Set up columns:
     - DeptID: INT, Primary Key, Auto Increment
     - DeptName: VARCHAR(255), NOT NULL
     - CollegeID: INT, Index
     - HeadOfDept: VARCHAR(255)

  **Step 4: Create Faculty Table**
  1. Click "Create table" again
  2. Table name: "tbl_faculty", Number of columns: 5
  3. Set up columns:
     - FacultyID: INT, Primary Key, Auto Increment
     - FacultyName: VARCHAR(255), NOT NULL
     - FacultyEmail: VARCHAR(255), UNIQUE
     - DeptID: INT, Index
     - Position: VARCHAR(100)

  **Step 5: Create Students Table**
  1. Click "Create table" again
  2. Table name: "tbl_students", Number of columns: 5
  3. Set up columns:
     - StudentID: INT, Primary Key, Auto Increment
     - StudentName: VARCHAR(255), NOT NULL
     - StudentEmail: VARCHAR(255), UNIQUE
     - DeptID: INT, Index
     - Year: INT

  **Step 6: Create Courses Table**
  1. Click "Create table" again
  2. Table name: "tbl_courses", Number of columns: 5
  3. Set up columns:
     - CourseID: INT, Primary Key, Auto Increment
     - CourseName: VARCHAR(255), NOT NULL
     - Credits: INT, NOT NULL
     - DeptID: INT, Index
     - FacultyID: INT, Index
  
  **Step 7: Set Up Relationships**
  1. Go to tbl_departments table → Set CollegeID to reference tbl_colleges(CollegeID)
  2. Go to tbl_faculty table → Set DeptID to reference tbl_departments(DeptID)
  3. Go to tbl_students table → Set DeptID to reference tbl_departments(DeptID)
  4. Go to tbl_courses table → Set DeptID to reference tbl_departments(DeptID)
  5. Go to tbl_courses table → Set FacultyID to reference tbl_faculty(FacultyID)
  
  ✅ **Done! Your normalized database is ready!**`,
      code: ``,
      language: "text",
    },
    {
      title: "Normalization Guide - SQL Code Method",
      description: `**Complete SQL Code to Create Normalized University Management Database**
  
  Copy and paste this code into phpMyAdmin's SQL tab:`,
      code: `-- Step 1: Create Database
  CREATE DATABASE university_management;
  USE university_management;
  
  -- Step 2: Create Colleges Table
  CREATE TABLE tbl_colleges (
      CollegeID INT PRIMARY KEY AUTO_INCREMENT,
      CollegeName VARCHAR(255) NOT NULL,
      Dean VARCHAR(255)
  );
  
  -- Step 3: Create Departments Table
  CREATE TABLE tbl_departments (
      DeptID INT PRIMARY KEY AUTO_INCREMENT,
      DeptName VARCHAR(255) NOT NULL,
      CollegeID INT,
      HeadOfDept VARCHAR(255),
      FOREIGN KEY (CollegeID) REFERENCES tbl_colleges(CollegeID)
  );

  -- Step 4: Create Faculty Table
  CREATE TABLE tbl_faculty (
      FacultyID INT PRIMARY KEY AUTO_INCREMENT,
      FacultyName VARCHAR(255) NOT NULL,
      FacultyEmail VARCHAR(255) UNIQUE,
      DeptID INT,
      Position VARCHAR(100),
      FOREIGN KEY (DeptID) REFERENCES tbl_departments(DeptID)
  );

  -- Step 5: Create Students Table
  CREATE TABLE tbl_students (
      StudentID INT PRIMARY KEY AUTO_INCREMENT,
      StudentName VARCHAR(255) NOT NULL,
      StudentEmail VARCHAR(255) UNIQUE,
      DeptID INT,
      Year INT,
      FOREIGN KEY (DeptID) REFERENCES tbl_departments(DeptID)
  );

  -- Step 6: Create Courses Table
  CREATE TABLE tbl_courses (
      CourseID INT PRIMARY KEY AUTO_INCREMENT,
      CourseName VARCHAR(255) NOT NULL,
      Credits INT NOT NULL,
      DeptID INT,
      FacultyID INT,
      FOREIGN KEY (DeptID) REFERENCES tbl_departments(DeptID),
      FOREIGN KEY (FacultyID) REFERENCES tbl_faculty(FacultyID)
  );
  
  -- Step 7: Insert Sample Data
  INSERT INTO tbl_colleges (CollegeName, Dean) VALUES
  ('College of Engineering', 'Dr. Anderson'),
  ('College of Sciences', 'Dr. Martinez'),
  ('College of Business', 'Dr. Thompson');

  INSERT INTO tbl_departments (DeptName, CollegeID, HeadOfDept) VALUES
  ('Computer Science', 1, 'Dr. Smith'),
  ('Mathematics', 2, 'Dr. Johnson'),
  ('Business Administration', 3, 'Dr. Brown');

  INSERT INTO tbl_faculty (FacultyName, FacultyEmail, DeptID, Position) VALUES
  ('Prof. Wilson', '<EMAIL>', 1, 'Professor'),
  ('Dr. Davis', '<EMAIL>', 1, 'Associate Professor'),
  ('Prof. Miller', '<EMAIL>', 2, 'Professor');

  INSERT INTO tbl_students (StudentName, StudentEmail, DeptID, Year) VALUES
  ('Alice Cooper', '<EMAIL>', 1, 3),
  ('Bob Taylor', '<EMAIL>', 1, 2),
  ('Carol White', '<EMAIL>', 2, 4);

  INSERT INTO tbl_courses (CourseName, Credits, DeptID, FacultyID) VALUES
  ('Database Systems', 3, 1, 1),
  ('Web Development', 4, 1, 2),
  ('Calculus III', 3, 2, 3);`,
      language: "sql",
    },
  ],
  relationships: [
    {
      title: "Understanding University Management Relationships",
      description: `**Why do we need relationships in our university management system?**
  
  In a university, we have five main entities:
  - **Colleges** (major divisions like Engineering, Sciences)
  - **Departments** (specific areas within colleges)
  - **Faculty** (professors and instructors)
  - **Students** (enrolled learners)
  - **Courses** (classes offered)
  
  **The Relationships:**
  1. **Colleges → Departments**: One college has multiple departments (1-to-Many)
  2. **Departments → Faculty**: One department has multiple faculty members (1-to-Many)
  3. **Departments → Students**: One department has multiple students (1-to-Many)
  4. **Departments → Courses**: One department offers multiple courses (1-to-Many)
  5. **Faculty → Courses**: One faculty member can teach multiple courses (1-to-Many)
  
  This way, we can easily track the university's organizational structure and academic offerings!`,
      code: ``,
      language: "text",
    },
  ],
};
