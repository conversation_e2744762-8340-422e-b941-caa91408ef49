// CRUD Operations Data - University Management System
export const crudData = {
  dbConnect: {
    title: "config.php - Database Connection",
    description: "This file handles the connection to your MySQL database",
    language: "php",
    code: `<?php
$servername = 'localhost';
$username = 'root';
$password = '';
$dbname = 'university_management';

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}
?>`,
  },
  indexPage: {
    title: "index.php - Homepage & Data Viewer",
    description:
      "Main interface with navigation links and comprehensive data display from all tables",
    language: "php",
    code: `<?php
include "config.php";
?>
<!DOCTYPE html>
<html>
<head>
    <title>University Management System</title>
    <link rel="stylesheet" href="universal-crud-styles.css">
</head>
<body>
    <div class="container">
        <h1>🏛️ University Management System</h1>
        
        <div class="nav-buttons">
            <a href="create.php" class="btn">➕ Add New Records</a>
            <a href="update.php" class="btn">✏️ Update Records</a>
            <a href="delete.php" class="btn">🗑️ Delete Records</a>
        </div>

        <!-- Statistics -->
        <h2>📊 University Statistics</h2>
        <div class="stats">
            <?php
            $colleges_count = $conn->query("SELECT COUNT(*) as count FROM colleges")->fetch_assoc()['count'];
            $departments_count = $conn->query("SELECT COUNT(*) as count FROM departments")->fetch_assoc()['count'];
            $faculty_count = $conn->query("SELECT COUNT(*) as count FROM faculty")->fetch_assoc()['count'];
            $students_count = $conn->query("SELECT COUNT(*) as count FROM students")->fetch_assoc()['count'];
            $courses_count = $conn->query("SELECT COUNT(*) as count FROM courses")->fetch_assoc()['count'];
            ?>
            <div class="stat-card">
                <h3><?php echo $colleges_count; ?></h3>
                <p>Total Colleges</p>
            </div>
            <div class="stat-card">
                <h3><?php echo $departments_count; ?></h3>
                <p>Total Departments</p>
            </div>
            <div class="stat-card">
                <h3><?php echo $faculty_count; ?></h3>
                <p>Total Faculty</p>
            </div>
            <div class="stat-card">
                <h3><?php echo $students_count; ?></h3>
                <p>Total Students</p>
            </div>
            <div class="stat-card">
                <h3><?php echo $courses_count; ?></h3>
                <p>Total Courses</p>
            </div>
        </div>

        <!-- Colleges and Departments -->
        <h2>🏢 Colleges and Departments</h2>
        <?php
        $colleges = $conn->query("
            SELECT c.college_id, c.college_name, c.dean, COUNT(d.department_id) as dept_count
            FROM colleges c
            LEFT JOIN departments d ON c.college_id = d.college_id
            GROUP BY c.college_id
            ORDER BY c.college_name
        ");
        echo "<table>";
        echo "<tr><th>College ID</th><th>College Name</th><th>Dean</th><th>Departments</th></tr>";
        while($college = $colleges->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $college['college_id'] . "</td>";
            echo "<td>" . $college['college_name'] . "</td>";
            echo "<td>" . $college['dean'] . "</td>";
            echo "<td>" . $college['dept_count'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        ?>

        <!-- Faculty Overview -->
        <h2>👨‍🏫 Faculty Overview</h2>
        <?php
        $faculty = $conn->query("
            SELECT f.faculty_id, f.faculty_name, f.position, d.department_name, c.college_name
            FROM faculty f
            JOIN departments d ON f.department_id = d.department_id
            JOIN colleges c ON d.college_id = c.college_id
            ORDER BY f.faculty_name LIMIT 10
        ");
        echo "<table>";
        echo "<tr><th>Faculty ID</th><th>Name</th><th>Position</th><th>Department</th><th>College</th></tr>";
        while($fac = $faculty->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $fac['faculty_id'] . "</td>";
            echo "<td>" . $fac['faculty_name'] . "</td>";
            echo "<td>" . $fac['position'] . "</td>";
            echo "<td>" . $fac['department_name'] . "</td>";
            echo "<td>" . $fac['college_name'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        ?>

        <!-- Course Enrollments -->
        <h2>📚 Popular Courses</h2>
        <?php
        $popular_courses = $conn->query("
            SELECT c.course_name, c.credits, f.faculty_name as instructor, 
                   COUNT(e.student_id) as enrollment_count
            FROM courses c
            LEFT JOIN faculty f ON c.instructor_id = f.faculty_id
            LEFT JOIN enrollments e ON c.course_id = e.course_id
            GROUP BY c.course_id
            ORDER BY enrollment_count DESC LIMIT 5
        ");
        echo "<table>";
        echo "<tr><th>Course</th><th>Credits</th><th>Instructor</th><th>Enrollments</th></tr>";
        while($course = $popular_courses->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $course['course_name'] . "</td>";
            echo "<td>" . $course['credits'] . "</td>";
            echo "<td>" . ($course['instructor'] ?: 'TBA') . "</td>";
            echo "<td>" . $course['enrollment_count'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        ?>
    </div>
</body>
</html>`,
  },
  createPage: {
    title: "create.php - Add New Records",
    description:
      "Forms for adding colleges, departments, faculty, students, and courses",
    language: "php",
    code: `<?php
include "config.php";

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $table = $_POST['table'];
    
    if ($table == 'colleges') {
        $sql = "INSERT INTO colleges (college_name, dean, established_year) VALUES (?, ?, ?)";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ssi", $_POST['college_name'], $_POST['dean'], $_POST['established_year']);
        
        if ($stmt->execute()) {
            echo "✅ College added successfully!";
        } else {
            echo "❌ Error: " . $conn->error;
        }
    }
    
    elseif ($table == 'departments') {
        $sql = "INSERT INTO departments (department_name, college_id, head_of_department) VALUES (?, ?, ?)";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("sis", $_POST['department_name'], $_POST['college_id'], $_POST['head_of_department']);
        
        if ($stmt->execute()) {
            echo "✅ Department added successfully!";
        } else {
            echo "❌ Error: " . $conn->error;
        }
    }
    
    elseif ($table == 'faculty') {
        $sql = "INSERT INTO faculty (faculty_name, department_id, position, email, hire_date) VALUES (?, ?, ?, ?, CURDATE())";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("siss", $_POST['faculty_name'], $_POST['department_id'], $_POST['position'], $_POST['email']);
        
        if ($stmt->execute()) {
            echo "✅ Faculty member added successfully!";
        } else {
            echo "❌ Error: " . $conn->error;
        }
    }
}
?>
<!DOCTYPE html>
<html>
<head>
    <title>Add New Records</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        input, select { margin: 5px 0; padding: 8px; width: 200px; }
        button { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>Add New Records</h1>
    <a href="index.php">← Back to Dashboard</a>

    <!-- Add College Form -->
    <div class="form-section">
        <h2>🏢 Add New College</h2>
        <form method="post">
            <input type="hidden" name="table" value="colleges">
            <input type="text" name="college_name" placeholder="College Name" required><br>
            <input type="text" name="dean" placeholder="Dean Name" required><br>
            <input type="number" name="established_year" placeholder="Established Year" min="1800" max="2024" required><br>
            <button type="submit">Add College</button>
        </form>
    </div>

    <!-- Add Department Form -->
    <div class="form-section">
        <h2>🏛️ Add New Department</h2>
        <form method="post">
            <input type="hidden" name="table" value="departments">
            <input type="text" name="department_name" placeholder="Department Name" required><br>
            <select name="college_id" required>
                <option value="">Select College</option>
                <?php
                $colleges = $conn->query("SELECT college_id, college_name FROM colleges ORDER BY college_name");
                while($college = $colleges->fetch_assoc()) {
                    echo "<option value='" . $college['college_id'] . "'>" . $college['college_name'] . "</option>";
                }
                ?>
            </select><br>
            <input type="text" name="head_of_department" placeholder="Head of Department" required><br>
            <button type="submit">Add Department</button>
        </form>
    </div>

    <!-- Add Faculty Form -->
    <div class="form-section">
        <h2>👨‍🏫 Add New Faculty</h2>
        <form method="post">
            <input type="hidden" name="table" value="faculty">
            <input type="text" name="faculty_name" placeholder="Faculty Name" required><br>
            <select name="department_id" required>
                <option value="">Select Department</option>
                <?php
                $departments = $conn->query("SELECT d.department_id, d.department_name, c.college_name FROM departments d JOIN colleges c ON d.college_id = c.college_id ORDER BY c.college_name, d.department_name");
                while($dept = $departments->fetch_assoc()) {
                    echo "<option value='" . $dept['department_id'] . "'>" . $dept['college_name'] . " - " . $dept['department_name'] . "</option>";
                }
                ?>
            </select><br>
            <select name="position" required>
                <option value="">Select Position</option>
                <option value="Professor">Professor</option>
                <option value="Associate Professor">Associate Professor</option>
                <option value="Assistant Professor">Assistant Professor</option>
                <option value="Lecturer">Lecturer</option>
            </select><br>
            <input type="email" name="email" placeholder="Email" required><br>
            <button type="submit">Add Faculty</button>
        </form>
    </div>
</body>
</html>`,
  },
  updatePage: {
    title: "update.php - Edit Records",
    description: "Forms for updating colleges, departments, and faculty",
    language: "php",
    code: `<?php
include "config.php";

if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['update'])) {
    $table = $_POST['table'];

    if ($table == 'colleges') {
        $sql = "UPDATE colleges SET college_name=?, dean=?, established_year=? WHERE college_id=?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ssii", $_POST['college_name'], $_POST['dean'], $_POST['established_year'], $_POST['college_id']);

        if ($stmt->execute()) {
            echo "✅ College updated successfully!";
        } else {
            echo "❌ Error: " . $conn->error;
        }
    }

    elseif ($table == 'faculty') {
        $sql = "UPDATE faculty SET faculty_name=?, position=?, email=? WHERE faculty_id=?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("sssi", $_POST['faculty_name'], $_POST['position'], $_POST['email'], $_POST['faculty_id']);

        if ($stmt->execute()) {
            echo "✅ Faculty updated successfully!";
        } else {
            echo "❌ Error: " . $conn->error;
        }
    }
}
?>
<!DOCTYPE html>
<html>
<head>
    <title>Update Records</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        input, select { margin: 5px 0; padding: 8px; width: 200px; }
        button { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; }
        .record { border: 1px solid #ccc; margin: 10px 0; padding: 10px; }
    </style>
</head>
<body>
    <h1>Update Records</h1>
    <a href="index.php">← Back to Dashboard</a>

    <!-- Update Colleges -->
    <div class="form-section">
        <h2>🏢 Update Colleges</h2>
        <?php
        $colleges = $conn->query("SELECT * FROM colleges ORDER BY college_name");
        while($college = $colleges->fetch_assoc()) {
            echo "<div class='record'>";
            echo "<form method='post'>";
            echo "<input type='hidden' name='table' value='colleges'>";
            echo "<input type='hidden' name='college_id' value='" . $college['college_id'] . "'>";
            echo "College ID: " . $college['college_id'] . "<br>";
            echo "Name: <input type='text' name='college_name' value='" . $college['college_name'] . "' required><br>";
            echo "Dean: <input type='text' name='dean' value='" . $college['dean'] . "' required><br>";
            echo "Established: <input type='number' name='established_year' value='" . $college['established_year'] . "' required><br>";
            echo "<button type='submit' name='update'>Update College</button>";
            echo "</form>";
            echo "</div>";
        }
        ?>
    </div>

    <!-- Update Faculty -->
    <div class="form-section">
        <h2>👨‍🏫 Update Faculty</h2>
        <?php
        $faculty = $conn->query("
            SELECT f.*, d.department_name, c.college_name
            FROM faculty f
            JOIN departments d ON f.department_id = d.department_id
            JOIN colleges c ON d.college_id = c.college_id
            ORDER BY f.faculty_name LIMIT 5
        ");
        while($fac = $faculty->fetch_assoc()) {
            echo "<div class='record'>";
            echo "<form method='post'>";
            echo "<input type='hidden' name='table' value='faculty'>";
            echo "<input type='hidden' name='faculty_id' value='" . $fac['faculty_id'] . "'>";
            echo "Faculty ID: " . $fac['faculty_id'] . " | Department: " . $fac['department_name'] . "<br>";
            echo "Name: <input type='text' name='faculty_name' value='" . $fac['faculty_name'] . "' required><br>";
            echo "Position: <select name='position'>";
            echo "<option value='Professor'" . ($fac['position'] == 'Professor' ? ' selected' : '') . ">Professor</option>";
            echo "<option value='Associate Professor'" . ($fac['position'] == 'Associate Professor' ? ' selected' : '') . ">Associate Professor</option>";
            echo "<option value='Assistant Professor'" . ($fac['position'] == 'Assistant Professor' ? ' selected' : '') . ">Assistant Professor</option>";
            echo "<option value='Lecturer'" . ($fac['position'] == 'Lecturer' ? ' selected' : '') . ">Lecturer</option>";
            echo "</select><br>";
            echo "Email: <input type='email' name='email' value='" . $fac['email'] . "' required><br>";
            echo "<button type='submit' name='update'>Update Faculty</button>";
            echo "</form>";
            echo "</div>";
        }
        ?>
    </div>
</body>
</html>`,
  },
  deletePage: {
    title: "delete.php - Remove Records",
    description:
      "Simple forms for deleting colleges, departments, faculty, students, and courses",
    language: "php",
    code: `<?php include "config.php"; ?>
<!DOCTYPE html>
<html>
<head>
    <title>Delete Records</title>
    <link rel="stylesheet" href="university-styles.css">
</head>
<body>
    <div class="container">
        <h1>🗑️ Delete Records</h1>
        <a href="index.php" class="btn">← Back to Main</a>

        <!-- Delete College -->
        <h2>🏢 Delete College</h2>
        <form method="POST" action="delete.php">
            <select name="college_id" required>
                <option value="">Select College to Delete</option>
                <?php
                $colleges = $conn->query("SELECT college_id, college_name FROM tbl_colleges");
                while($college = $colleges->fetch_assoc()) {
                    echo "<option value='{$college['college_id']}'>{$college['college_name']}</option>";
                }
                ?>
            </select>
            <button type="submit" name="delete_college" class="btn btn-danger">Delete College</button>
        </form>

        <!-- Delete Faculty -->
        <h2>👨‍🏫 Delete Faculty</h2>
        <form method="POST" action="delete.php">
            <select name="faculty_id" required>
                <option value="">Select Faculty to Delete</option>
                <?php
                $faculty = $conn->query("SELECT faculty_id, faculty_name FROM tbl_faculty");
                while($fac = $faculty->fetch_assoc()) {
                    echo "<option value='{$fac['faculty_id']}'>{$fac['faculty_name']}</option>";
                }
                ?>
            </select>
            <button type="submit" name="delete_faculty" class="btn btn-danger">Delete Faculty</button>
        </form>

        <!-- Delete Student -->
        <h2>👨‍🎓 Delete Student</h2>
        <form method="POST" action="delete.php">
            <select name="student_id" required>
                <option value="">Select Student to Delete</option>
                <?php
                $students = $conn->query("SELECT student_id, student_name FROM tbl_students");
                while($student = $students->fetch_assoc()) {
                    echo "<option value='{$student['student_id']}'>{$student['student_name']}</option>";
                }
                ?>
            </select>
            <button type="submit" name="delete_student" class="btn btn-danger">Delete Student</button>
        </form>

        <!-- Delete Course -->
        <h2>📚 Delete Course</h2>
        <form method="POST" action="delete.php">
            <select name="course_id" required>
                <option value="">Select Course to Delete</option>
                <?php
                $courses = $conn->query("SELECT course_id, course_name FROM tbl_courses");
                while($course = $courses->fetch_assoc()) {
                    echo "<option value='{$course['course_id']}'>{$course['course_name']}</option>";
                }
                ?>
            </select>
            <button type="submit" name="delete_course" class="btn btn-danger">Delete Course</button>
        </form>

        <?php
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Delete College
            if (isset($_POST['delete_college'])) {
                $college_id = $_POST['college_id'];
                $conn->query("DELETE FROM tbl_colleges WHERE college_id = '$college_id'");
                header("Location: delete.php");
            }

            // Delete Faculty
            if (isset($_POST['delete_faculty'])) {
                $faculty_id = $_POST['faculty_id'];
                $conn->query("DELETE FROM tbl_faculty WHERE faculty_id = '$faculty_id'");
                header("Location: delete.php");
            }

            // Delete Student
            if (isset($_POST['delete_student'])) {
                $student_id = $_POST['student_id'];
                $conn->query("DELETE FROM tbl_students WHERE student_id = '$student_id'");
                header("Location: delete.php");
            }

            // Delete Course
            if (isset($_POST['delete_course'])) {
                $course_id = $_POST['course_id'];
                $conn->query("DELETE FROM tbl_courses WHERE course_id = '$course_id'");
                header("Location: delete.php");
            }
        }
        ?>
    </div>
</body>
</html>`,
  },
  cssStyles: {
    title: "university-styles.css - Complete CSS for University Management",
    description: "Professional styling for all university management pages",
    language: "css",
    code: `/* University Management System Styles */
body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 20px;
    background-color: #f5f5f5;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    background-color: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

h1 {
    color: #2c3e50;
    text-align: center;
    margin-bottom: 30px;
    font-size: 2.5em;
}

h2 {
    color: #34495e;
    border-bottom: 2px solid #8e44ad;
    padding-bottom: 10px;
    margin-top: 30px;
}

.nav-buttons {
    text-align: center;
    margin-bottom: 30px;
}

.btn {
    display: inline-block;
    padding: 12px 24px;
    margin: 5px;
    background-color: #8e44ad;
    color: white;
    text-decoration: none;
    border-radius: 5px;
    border: none;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s;
}

.btn:hover {
    background-color: #7d3c98;
}

.btn-danger {
    background-color: #e74c3c;
}

.btn-danger:hover {
    background-color: #c0392b;
}

.section {
    margin-bottom: 40px;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background-color: #fafafa;
}

table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 15px;
}

th, td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

th {
    background-color: #8e44ad;
    color: white;
    font-weight: bold;
}

tr:hover {
    background-color: #f5f5f5;
}

form {
    background-color: white;
    padding: 20px;
    margin: 15px 0;
    border-radius: 8px;
    border: 1px solid #ddd;
}

input[type="text"], input[type="email"], input[type="number"], select {
    width: 100%;
    padding: 10px;
    margin: 8px 0;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-sizing: border-box;
    font-size: 16px;
}

input[type="text"]:focus, input[type="email"]:focus, input[type="number"]:focus, select:focus {
    border-color: #8e44ad;
    outline: none;
    box-shadow: 0 0 5px rgba(142, 68, 173, 0.3);
}

p {
    color: #7f8c8d;
    font-style: italic;
}

a {
    color: #8e44ad;
    text-decoration: none;
}

a:hover {
    color: #7d3c98;
    text-decoration: underline;
}`,
  },
};
