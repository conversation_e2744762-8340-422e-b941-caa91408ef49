// Index file for all CRUD data imports
import { crudData as beginnerCrudData } from "./beginner";
import { crudData as libraryCrudData } from "./library";
import { crudData as gradesCrudData } from "./grades";
import { crudData as tutorialCrudData } from "./tutorial";
import { crudData as ecommerceCrudData } from "./ecommerce";
import { crudData as universityCrudData } from "./university";
import { crudData as hospitalCrudData } from "./hospital";

// Export all CRUD data with scenario-specific keys
export const crudDataMap = {
  "Beginner crud": beginnerCrudData,
  library: libraryCrudData,
  grades: gradesCrudData,
  tutorial: tutorialCrudData,
  ecommerce: ecommerceCrudData,
  university: universityCrudData,
  hospital: hospitalCrudData,
};

// Helper function to get CRUD data by scenario ID
export function getCrudDataByScenario(scenarioId: string) {
  const baseData =
    crudDataMap[scenarioId as keyof typeof crudDataMap] || tutorialCrudData;

  // Return the data as-is since index.php now handles both navigation and data viewing
  return baseData;
}

// Export individual CRUD data for direct imports if needed
export {
  beginnerCrudData,
  libraryCrudData,
  gradesCrudData,
  tutorialCrudData,
  // ecommerceCrudData,
  universityCrudData,
  hospitalCrudData,
};
