// CRUD Operations Data - E-commerce System
export const crudData = {
  dbConnect: {
    title: "config.php - Database Connection",
    description: "This file handles the connection to your MySQL database",
    language: "php",
    code: `<?php
$servername = 'localhost';
$username = 'root';
$password = '';
$dbname = 'ecommerce_system';

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}
?>`,
  },
  indexPage: {
    title: "index.php - Homepage & Data Viewer",
    description:
      "Main interface with navigation links and comprehensive data display from all tables",
    language: "php",
    code: `<?php
include "config.php";
?>
<!DOCTYPE html>
<html>
<head>
    <title>E-commerce Management System</title>
    <link rel="stylesheet" href="ecommerce-styles.css">
</head>
<body>
    <div class="container">
        <h1>🛒 E-commerce Management System</h1>
        
        <div class="nav-buttons">
            <a href="create.php" class="btn">➕ Add New Records</a>
            <a href="update.php" class="btn">✏️ Update Records</a>
            <a href="delete.php" class="btn">🗑️ Delete Records</a>
        </div>

        <!-- Statistics -->
        <h2>📊 Business Statistics</h2>
        <div class="stats">
            <?php
            $products_count = $conn->query("SELECT COUNT(*) as count FROM products")->fetch_assoc()['count'];
            $customers_count = $conn->query("SELECT COUNT(*) as count FROM customers")->fetch_assoc()['count'];
            $orders_count = $conn->query("SELECT COUNT(*) as count FROM orders")->fetch_assoc()['count'];
            $total_revenue = $conn->query("SELECT SUM(total_amount) as total FROM orders")->fetch_assoc()['total'];
            ?>
            <div class="stat-card">
                <h3><?php echo $products_count; ?></h3>
                <p>Total Products</p>
            </div>
            <div class="stat-card">
                <h3><?php echo $customers_count; ?></h3>
                <p>Total Customers</p>
            </div>
            <div class="stat-card">
                <h3><?php echo $orders_count; ?></h3>
                <p>Total Orders</p>
            </div>
            <div class="stat-card">
                <h3>$<?php echo number_format($total_revenue ?: 0, 2); ?></h3>
                <p>Total Revenue</p>
            </div>
        </div>

        <!-- Products Table -->
        <h2>📦 Products</h2>
        <?php
        $products = $conn->query("SELECT * FROM products ORDER BY product_name LIMIT 10");
        echo "<table>";
        echo "<tr><th>Product ID</th><th>Name</th><th>Price</th><th>Stock</th><th>Category</th></tr>";
        while($product = $products->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $product['product_id'] . "</td>";
            echo "<td>" . $product['product_name'] . "</td>";
            echo "<td>$" . number_format($product['price'], 2) . "</td>";
            echo "<td>" . $product['stock_quantity'] . "</td>";
            echo "<td>" . $product['category'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        ?>

        <!-- Recent Orders -->
        <h2>📋 Recent Orders</h2>
        <?php
        $orders = $conn->query("
            SELECT o.order_id, c.customer_name, o.order_date, o.total_amount, o.status
            FROM orders o
            JOIN customers c ON o.customer_id = c.customer_id
            ORDER BY o.order_date DESC LIMIT 10
        ");
        echo "<table>";
        echo "<tr><th>Order ID</th><th>Customer</th><th>Date</th><th>Total</th><th>Status</th></tr>";
        while($order = $orders->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $order['order_id'] . "</td>";
            echo "<td>" . $order['customer_name'] . "</td>";
            echo "<td>" . $order['order_date'] . "</td>";
            echo "<td>$" . number_format($order['total_amount'], 2) . "</td>";
            echo "<td>" . $order['status'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        ?>

        <!-- Top Selling Products -->
        <h2>🏆 Top Selling Products</h2>
        <?php
        $top_products = $conn->query("
            SELECT p.product_name, SUM(oi.quantity) as total_sold, SUM(oi.quantity * oi.price) as revenue
            FROM order_items oi
            JOIN products p ON oi.product_id = p.product_id
            GROUP BY p.product_id
            ORDER BY total_sold DESC LIMIT 5
        ");
        echo "<table>";
        echo "<tr><th>Product</th><th>Units Sold</th><th>Revenue</th></tr>";
        while($top = $top_products->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $top['product_name'] . "</td>";
            echo "<td>" . $top['total_sold'] . "</td>";
            echo "<td>$" . number_format($top['revenue'], 2) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        ?>
    </div>
</body>
</html>`,
  },
  createPage: {
    title: "create.php - Add New Records",
    description:
      "Forms for adding products, customers, orders, and order items",
    language: "php",
    code: `<?php
include "config.php";

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $table = $_POST['table'];
    
    if ($table == 'products') {
        $sql = "INSERT INTO products (product_name, description, price, stock_quantity, category) VALUES (?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ssdis", $_POST['product_name'], $_POST['description'], $_POST['price'], $_POST['stock_quantity'], $_POST['category']);
        
        if ($stmt->execute()) {
            echo "✅ Product added successfully!";
        } else {
            echo "❌ Error: " . $conn->error;
        }
    }
    
    elseif ($table == 'customers') {
        $sql = "INSERT INTO customers (customer_name, email, phone, address) VALUES (?, ?, ?, ?)";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ssss", $_POST['customer_name'], $_POST['email'], $_POST['phone'], $_POST['address']);
        
        if ($stmt->execute()) {
            echo "✅ Customer added successfully!";
        } else {
            echo "❌ Error: " . $conn->error;
        }
    }
    
    elseif ($table == 'orders') {
        $sql = "INSERT INTO orders (customer_id, order_date, total_amount, status) VALUES (?, CURDATE(), ?, 'Pending')";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("id", $_POST['customer_id'], $_POST['total_amount']);
        
        if ($stmt->execute()) {
            echo "✅ Order created successfully!";
        } else {
            echo "❌ Error: " . $conn->error;
        }
    }
}
?>
<!DOCTYPE html>
<html>
<head>
    <title>Add New Records</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        input, select, textarea { margin: 5px 0; padding: 8px; width: 200px; }
        button { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>Add New Records</h1>
    <a href="index.php">← Back to Dashboard</a>

    <!-- Add Product Form -->
    <div class="form-section">
        <h2>📦 Add New Product</h2>
        <form method="post">
            <input type="hidden" name="table" value="products">
            <input type="text" name="product_name" placeholder="Product Name" required><br>
            <textarea name="description" placeholder="Description" rows="3"></textarea><br>
            <input type="number" name="price" placeholder="Price" step="0.01" min="0" required><br>
            <input type="number" name="stock_quantity" placeholder="Stock Quantity" min="0" required><br>
            <input type="text" name="category" placeholder="Category" required><br>
            <button type="submit">Add Product</button>
        </form>
    </div>

    <!-- Add Customer Form -->
    <div class="form-section">
        <h2>👤 Add New Customer</h2>
        <form method="post">
            <input type="hidden" name="table" value="customers">
            <input type="text" name="customer_name" placeholder="Customer Name" required><br>
            <input type="email" name="email" placeholder="Email" required><br>
            <input type="text" name="phone" placeholder="Phone Number" required><br>
            <textarea name="address" placeholder="Address" rows="3" required></textarea><br>
            <button type="submit">Add Customer</button>
        </form>
    </div>

    <!-- Create Order Form -->
    <div class="form-section">
        <h2>📋 Create New Order</h2>
        <form method="post">
            <input type="hidden" name="table" value="orders">
            <select name="customer_id" required>
                <option value="">Select Customer</option>
                <?php
                $customers = $conn->query("SELECT customer_id, customer_name FROM customers ORDER BY customer_name");
                while($customer = $customers->fetch_assoc()) {
                    echo "<option value='" . $customer['customer_id'] . "'>" . $customer['customer_name'] . "</option>";
                }
                ?>
            </select><br>
            <input type="number" name="total_amount" placeholder="Total Amount" step="0.01" min="0" required><br>
            <button type="submit">Create Order</button>
        </form>
    </div>
</body>
</html>`,
  },
  updatePage: {
    title: "update.php - Edit Records",
    description: "Forms for updating products, customers, and orders",
    language: "php",
    code: `<?php
include "config.php";

if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['update'])) {
    $table = $_POST['table'];

    if ($table == 'products') {
        $sql = "UPDATE products SET product_name=?, description=?, price=?, stock_quantity=?, category=? WHERE product_id=?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ssdisi", $_POST['product_name'], $_POST['description'], $_POST['price'], $_POST['stock_quantity'], $_POST['category'], $_POST['product_id']);

        if ($stmt->execute()) {
            echo "✅ Product updated successfully!";
        } else {
            echo "❌ Error: " . $conn->error;
        }
    }

    elseif ($table == 'customers') {
        $sql = "UPDATE customers SET customer_name=?, email=?, phone=?, address=? WHERE customer_id=?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ssssi", $_POST['customer_name'], $_POST['email'], $_POST['phone'], $_POST['address'], $_POST['customer_id']);

        if ($stmt->execute()) {
            echo "✅ Customer updated successfully!";
        } else {
            echo "❌ Error: " . $conn->error;
        }
    }

    elseif ($table == 'orders') {
        $sql = "UPDATE orders SET status=?, total_amount=? WHERE order_id=?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("sdi", $_POST['status'], $_POST['total_amount'], $_POST['order_id']);

        if ($stmt->execute()) {
            echo "✅ Order updated successfully!";
        } else {
            echo "❌ Error: " . $conn->error;
        }
    }
}
?>
<!DOCTYPE html>
<html>
<head>
    <title>Update Records</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        input, select, textarea { margin: 5px 0; padding: 8px; width: 200px; }
        button { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; }
        .record { border: 1px solid #ccc; margin: 10px 0; padding: 10px; }
    </style>
</head>
<body>
    <h1>Update Records</h1>
    <a href="index.php">← Back to Dashboard</a>

    <!-- Update Products -->
    <div class="form-section">
        <h2>📦 Update Products</h2>
        <?php
        $products = $conn->query("SELECT * FROM products ORDER BY product_name LIMIT 5");
        while($product = $products->fetch_assoc()) {
            echo "<div class='record'>";
            echo "<form method='post'>";
            echo "<input type='hidden' name='table' value='products'>";
            echo "<input type='hidden' name='product_id' value='" . $product['product_id'] . "'>";
            echo "Product ID: " . $product['product_id'] . "<br>";
            echo "Name: <input type='text' name='product_name' value='" . $product['product_name'] . "' required><br>";
            echo "Description: <textarea name='description' rows='2'>" . $product['description'] . "</textarea><br>";
            echo "Price: <input type='number' name='price' value='" . $product['price'] . "' step='0.01' required><br>";
            echo "Stock: <input type='number' name='stock_quantity' value='" . $product['stock_quantity'] . "' required><br>";
            echo "Category: <input type='text' name='category' value='" . $product['category'] . "' required><br>";
            echo "<button type='submit' name='update'>Update Product</button>";
            echo "</form>";
            echo "</div>";
        }
        ?>
    </div>

    <!-- Update Orders -->
    <div class="form-section">
        <h2>📋 Update Orders</h2>
        <?php
        $orders = $conn->query("
            SELECT o.*, c.customer_name
            FROM orders o
            JOIN customers c ON o.customer_id = c.customer_id
            ORDER BY o.order_date DESC LIMIT 5
        ");
        while($order = $orders->fetch_assoc()) {
            echo "<div class='record'>";
            echo "<form method='post'>";
            echo "<input type='hidden' name='table' value='orders'>";
            echo "<input type='hidden' name='order_id' value='" . $order['order_id'] . "'>";
            echo "Order ID: " . $order['order_id'] . " | Customer: " . $order['customer_name'] . "<br>";
            echo "Status: <select name='status'>";
            echo "<option value='Pending'" . ($order['status'] == 'Pending' ? ' selected' : '') . ">Pending</option>";
            echo "<option value='Processing'" . ($order['status'] == 'Processing' ? ' selected' : '') . ">Processing</option>";
            echo "<option value='Shipped'" . ($order['status'] == 'Shipped' ? ' selected' : '') . ">Shipped</option>";
            echo "<option value='Delivered'" . ($order['status'] == 'Delivered' ? ' selected' : '') . ">Delivered</option>";
            echo "<option value='Cancelled'" . ($order['status'] == 'Cancelled' ? ' selected' : '') . ">Cancelled</option>";
            echo "</select><br>";
            echo "Total: <input type='number' name='total_amount' value='" . $order['total_amount'] . "' step='0.01' required><br>";
            echo "<button type='submit' name='update'>Update Order</button>";
            echo "</form>";
            echo "</div>";
        }
        ?>
    </div>
</body>
</html>`,
  },
  deletePage: {
    title: "delete.php - Remove Records",
    description: "Safe deletion with confirmation and dependency checking",
    language: "php",
    code: `<?php
include "config.php";

if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['delete'])) {
    $table = $_POST['table'];
    $id = $_POST['id'];

    if ($table == 'products') {
        // Check if product is in any orders
        $check = $conn->query("SELECT COUNT(*) as count FROM order_items WHERE product_id='$id'");
        $row = $check->fetch_assoc();

        if ($row['count'] > 0) {
            echo "❌ Cannot delete product - it appears in " . $row['count'] . " order(s)!";
        } else {
            $sql = "DELETE FROM products WHERE product_id='$id'";
            if ($conn->query($sql)) {
                echo "✅ Product deleted successfully!";
            } else {
                echo "❌ Error: " . $conn->error;
            }
        }
    }

    elseif ($table == 'customers') {
        // Check if customer has orders
        $check = $conn->query("SELECT COUNT(*) as count FROM orders WHERE customer_id='$id'");
        $row = $check->fetch_assoc();

        if ($row['count'] > 0) {
            echo "❌ Cannot delete customer - they have " . $row['count'] . " order(s)!";
        } else {
            $sql = "DELETE FROM customers WHERE customer_id='$id'";
            if ($conn->query($sql)) {
                echo "✅ Customer deleted successfully!";
            } else {
                echo "❌ Error: " . $conn->error;
            }
        }
    }

    elseif ($table == 'orders') {
        // Delete order items first, then order
        $conn->query("DELETE FROM order_items WHERE order_id='$id'");
        $sql = "DELETE FROM orders WHERE order_id='$id'";
        if ($conn->query($sql)) {
            echo "✅ Order deleted successfully!";
        } else {
            echo "❌ Error: " . $conn->error;
        }
    }
}
?>
<!DOCTYPE html>
<html>
<head>
    <title>Delete Records</title>
    <link rel="stylesheet" href="universal-crud-styles.css">
</head>
<body>
    <h1>Delete Records</h1>
    <a href="index.php">← Back to Dashboard</a>

    <!-- Delete Products -->
    <h2>🗑️ Delete Products</h2>
    <table>
        <tr><th>Product ID</th><th>Name</th><th>Price</th><th>Stock</th><th>Action</th></tr>
        <?php
        $products = $conn->query("SELECT * FROM products ORDER BY product_name LIMIT 10");
        while($product = $products->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $product['product_id'] . "</td>";
            echo "<td>" . $product['product_name'] . "</td>";
            echo "<td>$" . number_format($product['price'], 2) . "</td>";
            echo "<td>" . $product['stock_quantity'] . "</td>";
            echo "<td>";
            echo "<form method='post' style='display:inline;' onsubmit='return confirm(\"Are you sure?\");'>";
            echo "<input type='hidden' name='table' value='products'>";
            echo "<input type='hidden' name='id' value='" . $product['product_id'] . "'>";
            echo "<button type='submit' name='delete' class='delete-btn'>Delete</button>";
            echo "</form>";
            echo "</td>";
            echo "</tr>";
        }
        ?>
    </table>

    <!-- Delete Orders -->
    <h2>🗑️ Delete Orders</h2>
    <table>
        <tr><th>Order ID</th><th>Customer</th><th>Date</th><th>Total</th><th>Status</th><th>Action</th></tr>
        <?php
        $orders = $conn->query("
            SELECT o.order_id, c.customer_name, o.order_date, o.total_amount, o.status
            FROM orders o
            JOIN customers c ON o.customer_id = c.customer_id
            ORDER BY o.order_date DESC LIMIT 10
        ");
        while($order = $orders->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $order['order_id'] . "</td>";
            echo "<td>" . $order['customer_name'] . "</td>";
            echo "<td>" . $order['order_date'] . "</td>";
            echo "<td>$" . number_format($order['total_amount'], 2) . "</td>";
            echo "<td>" . $order['status'] . "</td>";
            echo "<td>";
            echo "<form method='post' style='display:inline;' onsubmit='return confirm(\"Are you sure?\");'>";
            echo "<input type='hidden' name='table' value='orders'>";
            echo "<input type='hidden' name='id' value='" . $order['order_id'] . "'>";
            echo "<button type='submit' name='delete' class='delete-btn'>Delete</button>";
            echo "</form>";
            echo "</td>";
            echo "</tr>";
        }
        ?>
    </table>
</body>
</html>`,
  },
  cssStyles: {
    title: "ecommerce-styles.css - Complete CSS for E-commerce Management",
    description: "Professional styling for all e-commerce management pages",
    language: "css",
    code: `/* E-commerce Management System Styles */
body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 20px;
    background-color: #f5f5f5;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    background-color: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

h1 {
    color: #2c3e50;
    text-align: center;
    margin-bottom: 30px;
    font-size: 2.5em;
}

h2 {
    color: #34495e;
    border-bottom: 2px solid #e74c3c;
    padding-bottom: 10px;
    margin-top: 30px;
}

.nav-buttons {
    text-align: center;
    margin-bottom: 30px;
}

.btn {
    display: inline-block;
    padding: 12px 24px;
    margin: 5px;
    background-color: #e74c3c;
    color: white;
    text-decoration: none;
    border-radius: 5px;
    border: none;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s;
}

.btn:hover {
    background-color: #c0392b;
}

.btn-danger {
    background-color: #e74c3c;
}

.btn-danger:hover {
    background-color: #c0392b;
}

.section {
    margin-bottom: 40px;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background-color: #fafafa;
}

table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 15px;
}

th, td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

th {
    background-color: #e74c3c;
    color: white;
    font-weight: bold;
}

tr:hover {
    background-color: #f5f5f5;
}

form {
    background-color: white;
    padding: 20px;
    margin: 15px 0;
    border-radius: 8px;
    border: 1px solid #ddd;
}

input[type="text"], input[type="email"], input[type="number"], select, textarea {
    width: 100%;
    padding: 10px;
    margin: 8px 0;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-sizing: border-box;
    font-size: 16px;
}

input[type="text"]:focus, input[type="email"]:focus, input[type="number"]:focus, select:focus, textarea:focus {
    border-color: #e74c3c;
    outline: none;
    box-shadow: 0 0 5px rgba(231, 76, 60, 0.3);
}

p {
    color: #7f8c8d;
    font-style: italic;
}

a {
    color: #e74c3c;
    text-decoration: none;
}

a:hover {
    color: #c0392b;
    text-decoration: underline;
}`,
  },
};
