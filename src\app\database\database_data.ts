// CRUD Operations Data - Used for all database scenarios
export const crudData = {
  dbConnect: {
    title: "config.php - Database Connection",
    description: "This file handles the connection to your MySQL database",
    language: "php",
    code: `<?php
$servername = "localhost";
$username = "root";
$password = "";
$dbname = "tutorial_management";

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Set charset to utf8
$conn->set_charset("utf8");

echo "<!-- Database connected successfully -->";
?>`,
  },
  indexPage: {
    title: " index.php - Homepage with Navigation",
    description: "Main interface with links to all CRUD operations",
    language: "php",
    code: `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tutorial Management System</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f4f4f4; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .nav-buttons { display: flex; gap: 10px; margin: 20px 0; flex-wrap: wrap; }
        .btn { padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; }
        .btn:hover { background: #0056b3; }
        .btn-success { background: #28a745; }
        .btn-info { background: #17a2b8; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-danger { background: #dc3545; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }
        .stat-card { background: #e9ecef; padding: 15px; border-radius: 5px; text-align: center; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎓 Tutorial Management System</h1>
        <p>Welcome to the complete CRUD operations tutorial system. Use the buttons below to navigate:</p>

        <!-- Navigation Buttons -->
        <div class="nav-buttons">
            <a href="create.php" class="btn btn-success">➕ Add New Records</a>
            <a href="read.php" class="btn btn-info">📖 View All Data</a>
            <a href="update.php" class="btn btn-warning">✏️ Update Records</a>
            <a href="delete.php" class="btn btn-danger">🗑️ Delete Records</a>
        </div>

        <!-- Database Statistics -->
        <h2>📊 Database Statistics</h2>
        <div class="stats">
            <?php
            include 'config.php';

            // Count records in each table
            $units_count = $conn->query("SELECT COUNT(*) as count FROM tbl_units")->fetch_assoc()['count'];
            $tutors_count = $conn->query("SELECT COUNT(*) as count FROM tbl_tutors")->fetch_assoc()['count'];
            $students_count = $conn->query("SELECT COUNT(*) as count FROM tbl_students")->fetch_assoc()['count'];
            $enrollments_count = $conn->query("SELECT COUNT(*) as count FROM tbl_enrollment")->fetch_assoc()['count'];
            ?>

            <div class="stat-card">
                <h3><?php echo $units_count; ?></h3>
                <p>Total Units</p>
            </div>
            <div class="stat-card">
                <h3><?php echo $tutors_count; ?></h3>
                <p>Total Tutors</p>
            </div>
            <div class="stat-card">
                <h3><?php echo $students_count; ?></h3>
                <p>Total Students</p>
            </div>
            <div class="stat-card">
                <h3><?php echo $enrollments_count; ?></h3>
                <p>Total Enrollments</p>
            </div>
        </div>

        <!-- Quick Overview -->
        <h2>🔍 Recent Enrollments</h2>
        <table border="1" style="width: 100%; border-collapse: collapse;">
            <tr style="background-color: #f8f9fa;">
                <th style="padding: 10px;">Student</th>
                <th style="padding: 10px;">Unit</th>
                <th style="padding: 10px;">Tutor</th>
                <th style="padding: 10px;">Date</th>
                <th style="padding: 10px;">Grade</th>
            </tr>
            <?php
            $sql = "SELECT s.StudentName, u.Topic, t.TutorName, e.Date, e.Grade
                    FROM tbl_enrollment e
                    JOIN tbl_students s ON e.StudentID = s.StudentID
                    JOIN tbl_units u ON e.UnitID = u.UnitID
                    JOIN tbl_tutors t ON e.TutorID = t.TutorID
                    ORDER BY e.Date DESC LIMIT 5";
            $result = $conn->query($sql);

            if ($result->num_rows > 0) {
                while($row = $result->fetch_assoc()) {
                    echo "<tr>";
                    echo "<td style='padding: 8px;'>" . $row["StudentName"] . "</td>";
                    echo "<td style='padding: 8px;'>" . $row["Topic"] . "</td>";
                    echo "<td style='padding: 8px;'>" . $row["TutorName"] . "</td>";
                    echo "<td style='padding: 8px;'>" . $row["Date"] . "</td>";
                    echo "<td style='padding: 8px;'>" . ($row["Grade"] ?: "Not graded") . "</td>";
                    echo "</tr>";
                }
            } else {
                echo "<tr><td colspan='5' style='padding: 10px; text-align: center;'>No enrollments found</td></tr>";
            }
            $conn->close();
            ?>
        </table>

        <div style="margin-top: 20px; padding: 15px; background-color: #d4edda; border-radius: 5px;">
            <strong>💡 Tutorial System Features:</strong>
            <ul>
                <li>✅ Complete CRUD operations (Create, Read, Update, Delete)</li>
                <li>✅ Normalized database structure (3NF)</li>
                <li>✅ Foreign key relationships</li>
                <li>✅ Data validation and error handling</li>
                <li>✅ User-friendly interface</li>
            </ul>
        </div>
    </div>
</body>
</html>`,
  },
  createPage: {
    title: " create.php - Add New Records",
    description:
      "Complete forms for adding units, tutors, students, and enrollments",
    language: "php",
    code: `// This is a simplified version - full code available in tutorial
<?php
include 'config.php';

// Handle form submissions for all tables
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $table = $_POST['table'];

    if ($table == 'units') {
        $sql = "INSERT INTO tbl_units (UnitID, Topic, Book) VALUES (?, ?, ?)";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("sss", $_POST['UnitID'], $_POST['Topic'], $_POST['Book']);
    }
    // Similar forms for tutors, students, and enrollments...
}
?>

<!-- HTML forms for each table with proper validation -->
<form method="post">
    <input type="hidden" name="table" value="units">
    <input type="text" name="UnitID" required placeholder="Unit ID">
    <input type="text" name="Topic" required placeholder="Topic">
    <input type="text" name="Book" placeholder="Book">
    <button type="submit">Add Unit</button>
</form>`,
  },
  readPage: {
    title: " read.php - Display All Records",
    description: "Complete data viewing with search and filtering capabilities",
    language: "php",
    code: `<?php
include 'config.php';

// Display Units with enrollment count
$sql = "SELECT u.*, COUNT(e.EnrollmentID) as enrollment_count
        FROM tbl_units u
        LEFT JOIN tbl_enrollment e ON u.UnitID = e.UnitID
        GROUP BY u.UnitID ORDER BY u.Topic";
$result = $conn->query($sql);

echo "<h2>📚 Units</h2>";
echo "<table border='1'>";
echo "<tr><th>Unit ID</th><th>Topic</th><th>Book</th><th>Enrollments</th></tr>";

while($row = $result->fetch_assoc()) {
    echo "<tr>";
    echo "<td>" . $row["UnitID"] . "</td>";
    echo "<td>" . $row["Topic"] . "</td>";
    echo "<td>" . ($row["Book"] ?: "No book") . "</td>";
    echo "<td>" . $row["enrollment_count"] . "</td>";
    echo "</tr>";
}
echo "</table>";

// Similar displays for tutors, students, and enrollments...
?>`,
  },
  updatePage: {
    title: " update.php - Edit Records",
    description: "Forms for updating existing records with validation",
    language: "php",
    code: `<?php
include 'config.php';

// Handle update submissions
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['update'])) {
    $table = $_POST['table'];

    if ($table == 'units') {
        $sql = "UPDATE tbl_units SET Topic=?, Book=? WHERE UnitID=?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("sss", $_POST['Topic'], $_POST['Book'], $_POST['UnitID']);

        if ($stmt->execute()) {
            echo "✅ Unit updated successfully!";
        } else {
            echo "❌ Error: " . $conn->error;
        }
    }
    // Similar update logic for other tables...
}

// Display current records with edit forms
$units = $conn->query("SELECT * FROM tbl_units ORDER BY Topic");
echo "<h2>📚 Edit Units</h2>";

while($unit = $units->fetch_assoc()) {
    echo "<form method='post' style='border:1px solid #ddd; padding:10px; margin:10px;'>";
    echo "<input type='hidden' name='table' value='units'>";
    echo "<input type='hidden' name='UnitID' value='" . $unit['UnitID'] . "'>";
    echo "Unit ID: " . $unit['UnitID'] . "<br>";
    echo "Topic: <input type='text' name='Topic' value='" . $unit['Topic'] . "' required><br>";
    echo "Book: <input type='text' name='Book' value='" . $unit['Book'] . "'><br>";
    echo "<button type='submit' name='update'>Update Unit</button>";
    echo "</form>";
}
?>`,
  },
  deletePage: {
    title: " delete.php - Remove Records",
    description: "Safe deletion with confirmation and foreign key handling",
    language: "php",
    code: `<?php
include 'config.php';

// Handle deletion
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['delete'])) {
    $table = $_POST['table'];
    $id = $_POST['id'];

    if ($table == 'units') {
        // Check for dependencies first
        $check = $conn->query("SELECT COUNT(*) as count FROM tbl_enrollment WHERE UnitID='$id'");
        $row = $check->fetch_assoc();

        if ($row['count'] > 0) {
            echo "❌ Cannot delete unit - it has " . $row['count'] . " enrollments!";
        } else {
            $sql = "DELETE FROM tbl_units WHERE UnitID='$id'";
            if ($conn->query($sql)) {
                echo "✅ Unit deleted successfully!";
            } else {
                echo "❌ Error: " . $conn->error;
            }
        }
    }
    // Similar deletion logic for other tables...
}

// Display records with delete buttons
$units = $conn->query("SELECT * FROM tbl_units ORDER BY Topic");
echo "<h2>🗑️ Delete Units</h2>";
echo "<table border='1'>";
echo "<tr><th>Unit ID</th><th>Topic</th><th>Book</th><th>Action</th></tr>";

while($unit = $units->fetch_assoc()) {
    echo "<tr>";
    echo "<td>" . $unit['UnitID'] . "</td>";
    echo "<td>" . $unit['Topic'] . "</td>";
    echo "<td>" . ($unit['Book'] ?: "No book") . "</td>";
    echo "<td>";
    echo "<form method='post' style='display:inline;' onsubmit='return confirm(\"Are you sure?\");'>";
    echo "<input type='hidden' name='table' value='units'>";
    echo "<input type='hidden' name='id' value='" . $unit['UnitID'] . "'>";
    echo "<button type='submit' name='delete' style='background:red;color:white;'>Delete</button>";
    echo "</form>";
    echo "</td>";
    echo "</tr>";
}
echo "</table>";
?>`,
  },
};
