// CRUD Operations Data - Hospital Management System
export const crudData = {
  dbConnect: {
    title: "config.php - Database Connection",
    description: "This file handles the connection to your MySQL database",
    language: "php",
    code: `<?php
$servername = 'localhost';
$username = 'root';
$password = '';
$dbname = 'hospital_management';

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}
?>`,
  },
  indexPage: {
    title: "index.php - Homepage & Data Viewer",
    description:
      "Main interface with navigation links and comprehensive data display from all tables",
    language: "php",
    code: `<?php
include "config.php";
?>
<!DOCTYPE html>
<html>
<head>
    <title>Hospital Management System</title>
    <link rel="stylesheet" href="universal-crud-styles.css">
</head>
<body>
    <div class="container">
        <h1>🏥 Hospital Management System</h1>
        
        <div class="nav-buttons">
            <a href="create.php" class="btn">➕ Add New Records</a>
            <a href="update.php" class="btn">✏️ Update Records</a>
            <a href="delete.php" class="btn">🗑️ Delete Records</a>
        </div>

        <!-- Statistics -->
        <h2>📊 Hospital Statistics</h2>
        <div class="stats">
            <?php
            $doctors_count = $conn->query("SELECT COUNT(*) as count FROM doctors")->fetch_assoc()['count'];
            $patients_count = $conn->query("SELECT COUNT(*) as count FROM patients")->fetch_assoc()['count'];
            $appointments_today = $conn->query("SELECT COUNT(*) as count FROM appointments WHERE DATE(appointment_date) = CURDATE()")->fetch_assoc()['count'];
            $departments_count = $conn->query("SELECT COUNT(*) as count FROM departments")->fetch_assoc()['count'];
            ?>
            <div class="stat-card">
                <h3><?php echo $doctors_count; ?></h3>
                <p>Total Doctors</p>
            </div>
            <div class="stat-card">
                <h3><?php echo $patients_count; ?></h3>
                <p>Total Patients</p>
            </div>
            <div class="stat-card">
                <h3><?php echo $appointments_today; ?></h3>
                <p>Today's Appointments</p>
            </div>
            <div class="stat-card">
                <h3><?php echo $departments_count; ?></h3>
                <p>Departments</p>
            </div>
        </div>

        <!-- Today's Appointments -->
        <h2>📅 Today's Appointments</h2>
        <?php
        $today_appointments = $conn->query("
            SELECT a.appointment_id, p.patient_name, d.doctor_name, a.appointment_time, a.status, dept.department_name
            FROM appointments a
            JOIN patients p ON a.patient_id = p.patient_id
            JOIN doctors d ON a.doctor_id = d.doctor_id
            JOIN departments dept ON d.department_id = dept.department_id
            WHERE DATE(a.appointment_date) = CURDATE()
            ORDER BY a.appointment_time
        ");
        echo "<table>";
        echo "<tr><th>Appointment ID</th><th>Patient</th><th>Doctor</th><th>Department</th><th>Time</th><th>Status</th></tr>";
        while($appointment = $today_appointments->fetch_assoc()) {
            $row_class = '';
            if ($appointment['status'] == 'Urgent') $row_class = 'urgent';
            elseif ($appointment['status'] == 'Completed') $row_class = 'completed';
            
            echo "<tr class='$row_class'>";
            echo "<td>" . $appointment['appointment_id'] . "</td>";
            echo "<td>" . $appointment['patient_name'] . "</td>";
            echo "<td>" . $appointment['doctor_name'] . "</td>";
            echo "<td>" . $appointment['department_name'] . "</td>";
            echo "<td>" . $appointment['appointment_time'] . "</td>";
            echo "<td>" . $appointment['status'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        ?>

        <!-- Doctors by Department -->
        <h2>👨‍⚕️ Doctors by Department</h2>
        <?php
        $doctors_by_dept = $conn->query("
            SELECT dept.department_name, COUNT(d.doctor_id) as doctor_count, 
                   GROUP_CONCAT(d.doctor_name SEPARATOR ', ') as doctors
            FROM departments dept
            LEFT JOIN doctors d ON dept.department_id = d.department_id
            GROUP BY dept.department_id
            ORDER BY dept.department_name
        ");
        echo "<table>";
        echo "<tr><th>Department</th><th>Doctor Count</th><th>Doctors</th></tr>";
        while($dept = $doctors_by_dept->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $dept['department_name'] . "</td>";
            echo "<td>" . $dept['doctor_count'] . "</td>";
            echo "<td>" . ($dept['doctors'] ?: 'No doctors assigned') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        ?>

        <!-- Recent Treatments -->
        <h2>💊 Recent Treatments</h2>
        <?php
        $recent_treatments = $conn->query("
            SELECT t.treatment_id, p.patient_name, d.doctor_name, t.treatment_type, t.treatment_date, t.cost
            FROM treatments t
            JOIN patients p ON t.patient_id = p.patient_id
            JOIN doctors d ON t.doctor_id = d.doctor_id
            ORDER BY t.treatment_date DESC LIMIT 5
        ");
        echo "<table>";
        echo "<tr><th>Treatment ID</th><th>Patient</th><th>Doctor</th><th>Treatment</th><th>Date</th><th>Cost</th></tr>";
        while($treatment = $recent_treatments->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $treatment['treatment_id'] . "</td>";
            echo "<td>" . $treatment['patient_name'] . "</td>";
            echo "<td>" . $treatment['doctor_name'] . "</td>";
            echo "<td>" . $treatment['treatment_type'] . "</td>";
            echo "<td>" . $treatment['treatment_date'] . "</td>";
            echo "<td>$" . number_format($treatment['cost'], 2) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        ?>
    </div>
</body>
</html>`,
  },
  createPage: {
    title: "create.php - Add New Records",
    description:
      "Forms for adding doctors, patients, appointments, treatments, and departments",
    language: "php",
    code: `<?php
include "config.php";

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $table = $_POST['table'];
    
    if ($table == 'departments') {
        $sql = "INSERT INTO departments (department_name, head_doctor) VALUES (?, ?)";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ss", $_POST['department_name'], $_POST['head_doctor']);
        
        if ($stmt->execute()) {
            echo "✅ Department added successfully!";
        } else {
            echo "❌ Error: " . $conn->error;
        }
    }
    
    elseif ($table == 'doctors') {
        $sql = "INSERT INTO doctors (doctor_name, specialization, department_id, phone, email) VALUES (?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ssiss", $_POST['doctor_name'], $_POST['specialization'], $_POST['department_id'], $_POST['phone'], $_POST['email']);
        
        if ($stmt->execute()) {
            echo "✅ Doctor added successfully!";
        } else {
            echo "❌ Error: " . $conn->error;
        }
    }
    
    elseif ($table == 'patients') {
        $sql = "INSERT INTO patients (patient_name, age, gender, phone, address, emergency_contact) VALUES (?, ?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("sissss", $_POST['patient_name'], $_POST['age'], $_POST['gender'], $_POST['phone'], $_POST['address'], $_POST['emergency_contact']);
        
        if ($stmt->execute()) {
            echo "✅ Patient added successfully!";
        } else {
            echo "❌ Error: " . $conn->error;
        }
    }
    
    elseif ($table == 'appointments') {
        $sql = "INSERT INTO appointments (patient_id, doctor_id, appointment_date, appointment_time, status) VALUES (?, ?, ?, ?, 'Scheduled')";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("iiss", $_POST['patient_id'], $_POST['doctor_id'], $_POST['appointment_date'], $_POST['appointment_time']);
        
        if ($stmt->execute()) {
            echo "✅ Appointment scheduled successfully!";
        } else {
            echo "❌ Error: " . $conn->error;
        }
    }
}
?>
<!DOCTYPE html>
<html>
<head>
    <title>Add New Records</title>
    <link rel="stylesheet" href="universal-crud-styles.css">
</head>
<body>
    <h1>Add New Records</h1>
    <a href="index.php">← Back to Dashboard</a>

    <!-- Add Department Form -->
    <div class="form-section">
        <h2>🏥 Add New Department</h2>
        <form method="post">
            <input type="hidden" name="table" value="departments">
            <input type="text" name="department_name" placeholder="Department Name" required><br>
            <input type="text" name="head_doctor" placeholder="Head Doctor" required><br>
            <button type="submit">Add Department</button>
        </form>
    </div>

    <!-- Add Doctor Form -->
    <div class="form-section">
        <h2>👨‍⚕️ Add New Doctor</h2>
        <form method="post">
            <input type="hidden" name="table" value="doctors">
            <input type="text" name="doctor_name" placeholder="Doctor Name" required><br>
            <input type="text" name="specialization" placeholder="Specialization" required><br>
            <select name="department_id" required>
                <option value="">Select Department</option>
                <?php
                $departments = $conn->query("SELECT department_id, department_name FROM departments ORDER BY department_name");
                while($dept = $departments->fetch_assoc()) {
                    echo "<option value='" . $dept['department_id'] . "'>" . $dept['department_name'] . "</option>";
                }
                ?>
            </select><br>
            <input type="text" name="phone" placeholder="Phone Number" required><br>
            <input type="email" name="email" placeholder="Email" required><br>
            <button type="submit">Add Doctor</button>
        </form>
    </div>

    <!-- Add Patient Form -->
    <div class="form-section">
        <h2>👤 Add New Patient</h2>
        <form method="post">
            <input type="hidden" name="table" value="patients">
            <input type="text" name="patient_name" placeholder="Patient Name" required><br>
            <input type="number" name="age" placeholder="Age" min="0" max="150" required><br>
            <select name="gender" required>
                <option value="">Select Gender</option>
                <option value="Male">Male</option>
                <option value="Female">Female</option>
                <option value="Other">Other</option>
            </select><br>
            <input type="text" name="phone" placeholder="Phone Number" required><br>
            <textarea name="address" placeholder="Address" rows="3" required></textarea><br>
            <input type="text" name="emergency_contact" placeholder="Emergency Contact" required><br>
            <button type="submit">Add Patient</button>
        </form>
    </div>

    <!-- Schedule Appointment Form -->
    <div class="form-section">
        <h2>📅 Schedule Appointment</h2>
        <form method="post">
            <input type="hidden" name="table" value="appointments">
            <select name="patient_id" required>
                <option value="">Select Patient</option>
                <?php
                $patients = $conn->query("SELECT patient_id, patient_name FROM patients ORDER BY patient_name");
                while($patient = $patients->fetch_assoc()) {
                    echo "<option value='" . $patient['patient_id'] . "'>" . $patient['patient_name'] . "</option>";
                }
                ?>
            </select><br>
            <select name="doctor_id" required>
                <option value="">Select Doctor</option>
                <?php
                $doctors = $conn->query("SELECT d.doctor_id, d.doctor_name, dept.department_name FROM doctors d JOIN departments dept ON d.department_id = dept.department_id ORDER BY d.doctor_name");
                while($doctor = $doctors->fetch_assoc()) {
                    echo "<option value='" . $doctor['doctor_id'] . "'>" . $doctor['doctor_name'] . " (" . $doctor['department_name'] . ")</option>";
                }
                ?>
            </select><br>
            <input type="date" name="appointment_date" required><br>
            <input type="time" name="appointment_time" required><br>
            <button type="submit">Schedule Appointment</button>
        </form>
    </div>
</body>
</html>`,
  },
  updatePage: {
    title: "update.php - Edit Records",
    description: "Forms for updating doctors, patients, and appointments",
    language: "php",
    code: `<?php
include "config.php";

if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['update'])) {
    $table = $_POST['table'];

    if ($table == 'doctors') {
        $sql = "UPDATE doctors SET doctor_name=?, specialization=?, phone=?, email=? WHERE doctor_id=?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ssssi", $_POST['doctor_name'], $_POST['specialization'], $_POST['phone'], $_POST['email'], $_POST['doctor_id']);

        if ($stmt->execute()) {
            echo "✅ Doctor updated successfully!";
        } else {
            echo "❌ Error: " . $conn->error;
        }
    }

    elseif ($table == 'patients') {
        $sql = "UPDATE patients SET patient_name=?, age=?, gender=?, phone=?, address=?, emergency_contact=? WHERE patient_id=?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("sisssi", $_POST['patient_name'], $_POST['age'], $_POST['gender'], $_POST['phone'], $_POST['address'], $_POST['emergency_contact'], $_POST['patient_id']);

        if ($stmt->execute()) {
            echo "✅ Patient updated successfully!";
        } else {
            echo "❌ Error: " . $conn->error;
        }
    }

    elseif ($table == 'appointments') {
        $sql = "UPDATE appointments SET appointment_date=?, appointment_time=?, status=? WHERE appointment_id=?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("sssi", $_POST['appointment_date'], $_POST['appointment_time'], $_POST['status'], $_POST['appointment_id']);

        if ($stmt->execute()) {
            echo "✅ Appointment updated successfully!";
        } else {
            echo "❌ Error: " . $conn->error;
        }
    }
}
?>
<!DOCTYPE html>
<html>
<head>
    <title>Update Records</title>
    <link rel="stylesheet" href="universal-crud-styles.css">
</head>
<body>
    <h1>Update Records</h1>
    <a href="index.php">← Back to Dashboard</a>

    <!-- Update Doctors -->
    <div class="form-section">
        <h2>👨‍⚕️ Update Doctors</h2>
        <?php
        $doctors = $conn->query("
            SELECT d.*, dept.department_name
            FROM doctors d
            JOIN departments dept ON d.department_id = dept.department_id
            ORDER BY d.doctor_name LIMIT 5
        ");
        while($doctor = $doctors->fetch_assoc()) {
            echo "<div class='record'>";
            echo "<form method='post'>";
            echo "<input type='hidden' name='table' value='doctors'>";
            echo "<input type='hidden' name='doctor_id' value='" . $doctor['doctor_id'] . "'>";
            echo "Doctor ID: " . $doctor['doctor_id'] . " | Department: " . $doctor['department_name'] . "<br>";
            echo "Name: <input type='text' name='doctor_name' value='" . $doctor['doctor_name'] . "' required><br>";
            echo "Specialization: <input type='text' name='specialization' value='" . $doctor['specialization'] . "' required><br>";
            echo "Phone: <input type='text' name='phone' value='" . $doctor['phone'] . "' required><br>";
            echo "Email: <input type='email' name='email' value='" . $doctor['email'] . "' required><br>";
            echo "<button type='submit' name='update'>Update Doctor</button>";
            echo "</form>";
            echo "</div>";
        }
        ?>
    </div>

    <!-- Update Appointments -->
    <div class="form-section">
        <h2>📅 Update Appointments</h2>
        <?php
        $appointments = $conn->query("
            SELECT a.*, p.patient_name, d.doctor_name
            FROM appointments a
            JOIN patients p ON a.patient_id = p.patient_id
            JOIN doctors d ON a.doctor_id = d.doctor_id
            WHERE a.appointment_date >= CURDATE()
            ORDER BY a.appointment_date, a.appointment_time LIMIT 5
        ");
        while($appointment = $appointments->fetch_assoc()) {
            echo "<div class='record'>";
            echo "<form method='post'>";
            echo "<input type='hidden' name='table' value='appointments'>";
            echo "<input type='hidden' name='appointment_id' value='" . $appointment['appointment_id'] . "'>";
            echo "Patient: " . $appointment['patient_name'] . " | Doctor: " . $appointment['doctor_name'] . "<br>";
            echo "Date: <input type='date' name='appointment_date' value='" . $appointment['appointment_date'] . "' required><br>";
            echo "Time: <input type='time' name='appointment_time' value='" . $appointment['appointment_time'] . "' required><br>";
            echo "Status: <select name='status'>";
            echo "<option value='Scheduled'" . ($appointment['status'] == 'Scheduled' ? ' selected' : '') . ">Scheduled</option>";
            echo "<option value='Completed'" . ($appointment['status'] == 'Completed' ? ' selected' : '') . ">Completed</option>";
            echo "<option value='Cancelled'" . ($appointment['status'] == 'Cancelled' ? ' selected' : '') . ">Cancelled</option>";
            echo "<option value='Urgent'" . ($appointment['status'] == 'Urgent' ? ' selected' : '') . ">Urgent</option>";
            echo "</select><br>";
            echo "<button type='submit' name='update'>Update Appointment</button>";
            echo "</form>";
            echo "</div>";
        }
        ?>
    </div>
</body>
</html>`,
  },
  deletePage: {
    title: "delete.php - Remove Records",
    description: "Safe deletion with confirmation and dependency checking",
    language: "php",
    code: `<?php
include "config.php";

if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['delete'])) {
    $table = $_POST['table'];
    $id = $_POST['id'];

    if ($table == 'doctors') {
        // Check if doctor has appointments
        $check = $conn->query("SELECT COUNT(*) as count FROM appointments WHERE doctor_id='$id'");
        $row = $check->fetch_assoc();

        if ($row['count'] > 0) {
            echo "❌ Cannot delete doctor - they have " . $row['count'] . " appointment(s)!";
        } else {
            $sql = "DELETE FROM doctors WHERE doctor_id='$id'";
            if ($conn->query($sql)) {
                echo "✅ Doctor deleted successfully!";
            } else {
                echo "❌ Error: " . $conn->error;
            }
        }
    }

    elseif ($table == 'patients') {
        // Check if patient has appointments
        $check = $conn->query("SELECT COUNT(*) as count FROM appointments WHERE patient_id='$id'");
        $row = $check->fetch_assoc();

        if ($row['count'] > 0) {
            echo "❌ Cannot delete patient - they have " . $row['count'] . " appointment(s)!";
        } else {
            $sql = "DELETE FROM patients WHERE patient_id='$id'";
            if ($conn->query($sql)) {
                echo "✅ Patient deleted successfully!";
            } else {
                echo "❌ Error: " . $conn->error;
            }
        }
    }

    elseif ($table == 'appointments') {
        $sql = "DELETE FROM appointments WHERE appointment_id='$id'";
        if ($conn->query($sql)) {
            echo "✅ Appointment deleted successfully!";
        } else {
            echo "❌ Error: " . $conn->error;
        }
    }
}
?>
<!DOCTYPE html>
<html>
<head>
    <title>Delete Records</title>
    <link rel="stylesheet" href="universal-crud-styles.css">
</head>
<body>
    <h1>Delete Records</h1>
    <a href="index.php">← Back to Dashboard</a>

    <!-- Delete Doctors -->
    <h2>🗑️ Delete Doctors</h2>
    <table>
        <tr><th>Doctor ID</th><th>Name</th><th>Specialization</th><th>Department</th><th>Action</th></tr>
        <?php
        $doctors = $conn->query("
            SELECT d.doctor_id, d.doctor_name, d.specialization, dept.department_name
            FROM doctors d
            JOIN departments dept ON d.department_id = dept.department_id
            ORDER BY d.doctor_name
        ");
        while($doctor = $doctors->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $doctor['doctor_id'] . "</td>";
            echo "<td>" . $doctor['doctor_name'] . "</td>";
            echo "<td>" . $doctor['specialization'] . "</td>";
            echo "<td>" . $doctor['department_name'] . "</td>";
            echo "<td>";
            echo "<form method='post' style='display:inline;' onsubmit='return confirm(\"Are you sure?\");'>";
            echo "<input type='hidden' name='table' value='doctors'>";
            echo "<input type='hidden' name='id' value='" . $doctor['doctor_id'] . "'>";
            echo "<button type='submit' name='delete' class='delete-btn'>Delete</button>";
            echo "</form>";
            echo "</td>";
            echo "</tr>";
        }
        ?>
    </table>

    <!-- Delete Appointments -->
    <h2>🗑️ Delete Appointments</h2>
    <table>
        <tr><th>Appointment ID</th><th>Patient</th><th>Doctor</th><th>Date</th><th>Time</th><th>Status</th><th>Action</th></tr>
        <?php
        $appointments = $conn->query("
            SELECT a.appointment_id, p.patient_name, d.doctor_name, a.appointment_date, a.appointment_time, a.status
            FROM appointments a
            JOIN patients p ON a.patient_id = p.patient_id
            JOIN doctors d ON a.doctor_id = d.doctor_id
            ORDER BY a.appointment_date DESC LIMIT 10
        ");
        while($appointment = $appointments->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $appointment['appointment_id'] . "</td>";
            echo "<td>" . $appointment['patient_name'] . "</td>";
            echo "<td>" . $appointment['doctor_name'] . "</td>";
            echo "<td>" . $appointment['appointment_date'] . "</td>";
            echo "<td>" . $appointment['appointment_time'] . "</td>";
            echo "<td>" . $appointment['status'] . "</td>";
            echo "<td>";
            echo "<form method='post' style='display:inline;' onsubmit='return confirm(\"Are you sure?\");'>";
            echo "<input type='hidden' name='table' value='appointments'>";
            echo "<input type='hidden' name='id' value='" . $appointment['appointment_id'] . "'>";
            echo "<button type='submit' name='delete' class='delete-btn'>Delete</button>";
            echo "</form>";
            echo "</td>";
            echo "</tr>";
        }
        ?>
    </table>
</body>
</html>`,
  },
  cssStyles: {
    title: "hospital-styles.css - Complete CSS for Hospital Management",
    description: "Professional styling for all hospital management pages",
    language: "css",
    code: `/* Hospital Management System Styles */
body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 20px;
    background-color: #f5f5f5;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    background-color: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

h1 {
    color: #2c3e50;
    text-align: center;
    margin-bottom: 30px;
    font-size: 2.5em;
}

h2 {
    color: #34495e;
    border-bottom: 2px solid #27ae60;
    padding-bottom: 10px;
    margin-top: 30px;
}

.nav-buttons {
    text-align: center;
    margin-bottom: 30px;
}

.btn {
    display: inline-block;
    padding: 12px 24px;
    margin: 5px;
    background-color: #27ae60;
    color: white;
    text-decoration: none;
    border-radius: 5px;
    border: none;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s;
}

.btn:hover {
    background-color: #229954;
}

.btn-danger {
    background-color: #e74c3c;
}

.btn-danger:hover {
    background-color: #c0392b;
}

.section {
    margin-bottom: 40px;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background-color: #fafafa;
}

table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 15px;
}

th, td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

th {
    background-color: #27ae60;
    color: white;
    font-weight: bold;
}

tr:hover {
    background-color: #f5f5f5;
}

form {
    background-color: white;
    padding: 20px;
    margin: 15px 0;
    border-radius: 8px;
    border: 1px solid #ddd;
}

input[type="text"], input[type="email"], input[type="number"], input[type="date"], input[type="time"], select, textarea {
    width: 100%;
    padding: 10px;
    margin: 8px 0;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-sizing: border-box;
    font-size: 16px;
}

input[type="text"]:focus, input[type="email"]:focus, input[type="number"]:focus, input[type="date"]:focus, input[type="time"]:focus, select:focus, textarea:focus {
    border-color: #27ae60;
    outline: none;
    box-shadow: 0 0 5px rgba(39, 174, 96, 0.3);
}

p {
    color: #7f8c8d;
    font-style: italic;
}

a {
    color: #27ae60;
    text-decoration: none;
}

a:hover {
    color: #229954;
    text-decoration: underline;
}`,
  },
};
