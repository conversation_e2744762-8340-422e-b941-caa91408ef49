import { Database, Download, BookOpen } from "lucide-react";

export interface Tab {
  id: string;
  label: string;
  icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
}

// Tab configuration
export const tabs: Tab[] = [
  { id: "basics", label: "Basic Commands", icon: BookOpen },
  { id: "setup", label: "XAMPP Setup", icon: Download },
  { id: "scenarios", label: "Table Scenarios", icon: Database },
];
