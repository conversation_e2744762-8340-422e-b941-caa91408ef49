"use client";

export default function Setup() {
  return (
    <div className="space-y-4 sm:space-y-6">
      <h2 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 dark:text-white">
        XAMPP Setup
      </h2>

      <div className="space-y-8">
        <div className="space-y-4">
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
            Step 1: Download XAMPP
          </h3>
          <p className="text-base text-gray-600 dark:text-gray-400">
            Go to:{" "}
            <a
              href="https://www.apachefriends.org/download.html"
              target="_blank"
              className="text-blue-600 dark:text-blue-400 hover:underline break-all"
            >
              https://www.apachefriends.org/download.html
            </a>
          </p>
          <p className="text-base text-gray-600 dark:text-gray-400">
            Choose your operating system and download XAMPP.
          </p>
        </div>

        <div className="space-y-4">
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
            Step 2: Install XAMPP
          </h3>
          <ul className="list-disc list-inside space-y-2 text-base text-gray-600 dark:text-gray-400">
            <li>Run the downloaded installer</li>
            <li>Follow the installation wizard</li>
            <li>Select Apache, MySQL, and phpMyAdmin</li>
            <li>Complete the installation</li>
          </ul>
        </div>

        <div className="space-y-4">
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
            Step 3: Start Services
          </h3>
          <ul className="list-disc list-inside space-y-2 text-base text-gray-600 dark:text-gray-400">
            <li>Open XAMPP Control Panel</li>
            <li>
              Click <b>Start</b> for Apache
            </li>
            <li>
              Click <b>Start</b> for MySQL
            </li>
            <li>Both should show Running status</li>
          </ul>
        </div>

        <div className="space-y-4">
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
            Step 4: Test Installation
          </h3>
          <ul className="list-disc list-inside space-y-2 text-base text-gray-600 dark:text-gray-400">
            <li>
              Open browser and go to:{" "}
              <code className="bg-gray-200 dark:bg-gray-700 px-2 py-1 rounded text-sm font-mono">
                http://localhost
              </code>
            </li>
            <li>You should see XAMPP dashboard</li>
            <li>
              Click <b>phpMyAdmin</b> to access database management
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
}
