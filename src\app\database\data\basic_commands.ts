export interface CodeExample {
  title: string;
  description: string;
  code: string;
}

// Basic Commands Data
export const basicCommandsData: CodeExample[] = [
  {
    title: "1. PHP Variables",
    description:
      "Variables in PHP start with $ and can store different data types",
    code: `<?php
  // Variables (start with $)
  $name = "<PERSON>";
  $age = 25;
  $height = 5.9;
  $is_student = true;
  
  // Output to browser
  echo "Hello, " . $name;
  print "Age: " . $age;
  ?>`,
  },
  {
    title: "2. PHP Arrays",
    description: "Simple and associative arrays for storing multiple values",
    code: `<?php
  // Simple arrays
  $fruits = array("apple", "banana", "orange");
  $colors = ["red", "green", "blue"];  // Short syntax
  
  // Associative arrays (like objects)
  $person = array(
      "name" => "Alice",
      "age" => 30,
      "email" => "<EMAIL>"
  );
  
  // Access array elements
  echo $fruits[0];  // apple
  echo $person["name"];  // Alice
  ?>`,
  },
  {
    title: "3. PHP Functions",
    description: "Creating and calling functions with parameters",
    code: `<?php
  // Function definition
  function greet($username) {
      return "Welcome, " . $username . "!";
  }
  
  // Function call
  echo greet("Alice");
  
  // Function with multiple parameters
  function calculateTotal($price, $tax) {
      return $price + ($price * $tax);
  }
  
  echo calculateTotal(100, 0.08);  // 108
  ?>`,
  },
  {
    title: "4. PHP If-Else and Loops",
    description: "Control structures for conditional logic and iteration",
    code: `<?php
  $age = 20;
  
  // If-else statement
  if ($age >= 18) {
      echo "Adult";
  } else {
      echo "Minor";
  }
  
  // For loop
  for ($i = 0; $i < 5; $i++) {
      echo $i . " ";
  }
  
  // Foreach loop
  $fruits = ["apple", "banana", "orange"];
  foreach ($fruits as $fruit) {
      echo $fruit . " ";
  }
  ?>`,
  },
  {
    title: "5. PHP MySQLi Connection",
    description: "Connecting to MySQL database using MySQLi",
    code: `<?php
  // Database connection variables
  $servername = "localhost";
  $username = "root";
  $password = "";
  $dbname = "tutorial_system";
  
  // Create connection
  $conn = new mysqli($servername, $username, $password, $dbname);
  
  // Check connection
  if ($conn->connect_error) {
      die("Connection failed: " . $conn->connect_error);
  }
  
  echo "Connected successfully";
  $conn->close();
  ?>`,
  },
  {
    title: "6. PHP Include and Require",
    description: "Including external PHP files for code reusability",
    code: `<?php
  // Include a file (continues if file not found)
  include 'header.php';
  include_once 'config.php';  // Include only once
  
  // Require a file (stops execution if file not found)
  require 'database.php';
  require_once 'functions.php';  // Require only once
  
  // Example usage
  require_once 'config.php';
  include 'navigation.php';
  ?>`,
  },
  {
    title: "7. Database Operations",
    description: "Basic commands for creating and managing databases",
    code: `-- Create a new database
  CREATE DATABASE my_database;
  
  -- Use a specific database
  USE my_database;
  
  -- Show all databases
  SHOW DATABASES;
  
  -- Drop a database (be careful!)
  DROP DATABASE my_database;`,
  },
  {
    title: "8. Table Operations",
    description: "Commands for creating and managing tables",
    code: `-- Create a new table
  CREATE TABLE students (
      id INT PRIMARY KEY AUTO_INCREMENT,
      name VARCHAR(100) NOT NULL,
      email VARCHAR(100) UNIQUE,
      age INT
  );
  
  -- Show all tables
  SHOW TABLES;
  
  -- Describe table structure
  DESCRIBE students;
  
  -- Drop a table
  DROP TABLE students;`,
  },
  {
    title: "9. Data Manipulation (CRUD)",
    description: "Insert, Select, Update, and Delete operations",
    code: `-- INSERT: Add new data
  INSERT INTO students (name, email, age)
  VALUES ('John Doe', '<EMAIL>', 20);
  
  -- SELECT: Retrieve data
  SELECT * FROM students;
  SELECT name, email FROM students WHERE age > 18;
  
  -- UPDATE: Modify existing data
  UPDATE students
  SET age = 21
  WHERE name = 'John Doe';
  
  -- DELETE: Remove data
  DELETE FROM students WHERE id = 1;`,
  },
  {
    title: "10. Common Data Types",
    description: "Frequently used MySQL data types",
    code: `-- Numeric Types
  INT              -- Integer numbers
  DECIMAL(10,2)    -- Decimal numbers with precision
  FLOAT            -- Floating point numbers
  
  -- String Types
  VARCHAR(255)     -- Variable length string
  CHAR(10)         -- Fixed length string
  TEXT             -- Long text
  
  -- Date/Time Types
  DATE             -- Date (YYYY-MM-DD)
  TIME             -- Time (HH:MM:SS)
  DATETIME         -- Date and time
  TIMESTAMP        -- Timestamp
  
  -- Other Types
  BOOLEAN          -- True/False values
  ENUM('A','B','C') -- Predefined values`,
  },
  {
    title: "11. Constraints",
    description: "Rules to ensure data integrity",
    code: `-- Primary Key
  CREATE TABLE users (
      id INT PRIMARY KEY AUTO_INCREMENT,
      username VARCHAR(50) NOT NULL
  );
  
  -- Foreign Key
  CREATE TABLE orders (
      id INT PRIMARY KEY AUTO_INCREMENT,
      user_id INT,
      FOREIGN KEY (user_id) REFERENCES users(id)
  );
  
  -- Unique Constraint
  ALTER TABLE users ADD CONSTRAINT UNIQUE (username);
  
  -- Check Constraint
  ALTER TABLE users ADD CONSTRAINT CHECK (age >= 0);
  
  -- Not Null
  ALTER TABLE users MODIFY COLUMN email VARCHAR(100) NOT NULL;`,
  },
  {
    title: "12. JOIN Operations",
    description:
      "Combine data from multiple tables using different types of joins",
    code: `-- INNER JOIN: Returns only matching records
  SELECT s.name, e.grade, u.topic
  FROM students s
  INNER JOIN enrollments e ON s.id = e.student_id
  INNER JOIN units u ON e.unit_id = u.id;
  
  -- LEFT JOIN: Returns all records from left table
  SELECT s.name, e.grade
  FROM students s
  LEFT JOIN enrollments e ON s.id = e.student_id;
  
  -- RIGHT JOIN: Returns all records from right table
  SELECT s.name, e.grade
  FROM students s
  RIGHT JOIN enrollments e ON s.id = e.student_id;
  
  -- FULL OUTER JOIN: Returns all records from both tables
  SELECT s.name, e.grade
  FROM students s
  FULL OUTER JOIN enrollments e ON s.id = e.student_id;`,
  },
  {
    title: "13. Aggregate Functions",
    description: "Functions that perform calculations on multiple rows",
    code: `-- COUNT: Count number of records
  SELECT COUNT(*) FROM students;
  SELECT COUNT(DISTINCT age) FROM students;
  
  -- SUM: Calculate total
  SELECT SUM(salary) FROM employees;
  
  -- AVG: Calculate average
  SELECT AVG(age) FROM students;
  
  -- MAX and MIN: Find maximum and minimum values
  SELECT MAX(age), MIN(age) FROM students;
  
  -- GROUP BY: Group results by column
  SELECT age, COUNT(*) as student_count
  FROM students
  GROUP BY age;
  
  -- HAVING: Filter grouped results
  SELECT age, COUNT(*) as count
  FROM students
  GROUP BY age
  HAVING COUNT(*) > 1;`,
  },
  {
    title: "14. Sorting and Filtering",
    description: "Order and filter query results",
    code: `-- ORDER BY: Sort results
  SELECT * FROM students ORDER BY name ASC;
  SELECT * FROM students ORDER BY age DESC;
  SELECT * FROM students ORDER BY age DESC, name ASC;
  
  -- WHERE: Filter records
  SELECT * FROM students WHERE age > 18;
  SELECT * FROM students WHERE age BETWEEN 18 AND 25;
  SELECT * FROM students WHERE name LIKE 'John%';
  SELECT * FROM students WHERE age IN (18, 19, 20);
  
  -- LIMIT: Restrict number of results
  SELECT * FROM students LIMIT 10;
  SELECT * FROM students LIMIT 5 OFFSET 10;
  
  -- DISTINCT: Remove duplicates
  SELECT DISTINCT age FROM students;`,
  },
  {
    title: "15. ALTER TABLE Operations",
    description: "Modify existing table structure",
    code: `-- Add new column
  ALTER TABLE students ADD COLUMN phone VARCHAR(15);
  
  -- Modify column data type
  ALTER TABLE students MODIFY COLUMN age SMALLINT;
  
  -- Rename column
  ALTER TABLE students CHANGE COLUMN phone phone_number VARCHAR(15);
  
  -- Drop column
  ALTER TABLE students DROP COLUMN phone_number;
  
  -- Add index
  ALTER TABLE students ADD INDEX idx_name (name);
  
  -- Add foreign key constraint
  ALTER TABLE enrollments
  ADD CONSTRAINT fk_student
  FOREIGN KEY (student_id) REFERENCES students(id);
  
  -- Drop constraint
  ALTER TABLE enrollments DROP FOREIGN KEY fk_student;`,
  },
  {
    title: "16. Indexes",
    description: "Improve query performance with indexes",
    code: `-- Create index on single column
  CREATE INDEX idx_student_name ON students(name);
  
  -- Create index on multiple columns
  CREATE INDEX idx_student_age_name ON students(age, name);
  
  -- Create unique index
  CREATE UNIQUE INDEX idx_student_email ON students(email);
  
  -- Show indexes for a table
  SHOW INDEX FROM students;
  
  -- Drop index
  DROP INDEX idx_student_name ON students;
  
  -- Primary key automatically creates index
  -- Foreign keys automatically create indexes`,
  },
  {
    title: "17. HTML Document Structure",
    description: "Basic HTML5 document structure with semantic elements",
    code: `<!DOCTYPE html>
  <html lang="en">
  <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Tutorial System</title>
      <link rel="stylesheet" href="styles.css">
  </head>
  <body>
      <header>
          <h1>Tutorial Management System</h1>
      </header>
      <main>
          <h2>Welcome</h2>
          <p>Manage your tutorial sessions efficiently.</p>
      </main>
      <footer>
          <p>&copy; 2024 Tutorial System</p>
      </footer>
  </body>
  </html>`,
  },
  {
    title: "18. HTML Form Elements",
    description: "Creating forms for user input with various input types",
    code: `<form action="process.php" method="POST">
      <label for="name">Student Name:</label>
      <input type="text" id="name" name="student_name" required>
  
      <label for="email">Email:</label>
      <input type="email" id="email" name="email" required>
  
      <label for="unit">Select Unit:</label>
      <select id="unit" name="unit_id">
          <option value="U001">Database Design</option>
          <option value="U002">Web Development</option>
      </select>
  
      <button type="submit">Submit</button>
  </form>`,
  },
  {
    title: "19. HTML Table Structure",
    description: "Creating tables to display structured data",
    code: `<table>
      <thead>
          <tr>
              <th>Student ID</th>
              <th>Name</th>
              <th>Email</th>
              <th>Actions</th>
          </tr>
      </thead>
      <tbody>
          <tr>
              <td>S001</td>
              <td>Alice Johnson</td>
              <td><EMAIL></td>
              <td>
                  <a href="edit.php?id=S001">Edit</a>
                  <a href="delete.php?id=S001">Delete</a>
              </td>
          </tr>
      </tbody>
  </table>`,
  },
  {
    title: "20. CSS Reset and Body Styles",
    description: "Basic CSS reset and body styling for consistent appearance",
    code: `/* Reset and basic styling */
  * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
  }
  
  body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      background-color: #f4f4f4;
  }`,
  },
  {
    title: "21. CSS Header and Navigation",
    description: "Styling header and navigation elements",
    code: `/* Header styling */
  header {
      background-color: #2c3e50;
      color: white;
      padding: 1rem 0;
      text-align: center;
  }
  
  /* Navigation */
  nav ul {
      list-style: none;
      display: flex;
      justify-content: center;
      gap: 2rem;
  }
  
  nav a {
      color: white;
      text-decoration: none;
      padding: 0.5rem 1rem;
      border-radius: 4px;
  }
  
  nav a:hover {
      background-color: #34495e;
  }`,
  },
  {
    title: "22. CSS Form and Table Styling",
    description: "Styling forms and tables for better user experience",
    code: `/* Form styling */
  form {
      background-color: #f8f9fa;
      padding: 2rem;
      border-radius: 8px;
      margin: 2rem 0;
  }
  
  input, select {
      width: 100%;
      padding: 0.75rem;
      margin-bottom: 1rem;
      border: 1px solid #ddd;
      border-radius: 4px;
  }
  
  button {
      background-color: #3498db;
      color: white;
      padding: 0.75rem 2rem;
      border: none;
      border-radius: 4px;
      cursor: pointer;
  }
  
  /* Table styling */
  table {
      width: 100%;
      border-collapse: collapse;
      margin: 2rem 0;
  }
  
  th, td {
      padding: 1rem;
      text-align: left;
      border-bottom: 1px solid #ddd;
  }
  
  th {
      background-color: #f8f9fa;
  }`,
  },
  {
    title: "23. Linking CSS in HTML",
    description: "Different methods to include CSS in HTML documents",
    code: `<!-- External CSS file (recommended) -->
  <link rel="stylesheet" href="styles.css">
  
  <!-- Internal CSS (in <head> section) -->
  <style>
      body {
          font-family: Arial, sans-serif;
      }
  </style>
  
  <!-- Inline CSS (not recommended) -->
  <div style="color: blue; padding: 10px;">
      This div has inline styles
  </div>`,
  },
];
