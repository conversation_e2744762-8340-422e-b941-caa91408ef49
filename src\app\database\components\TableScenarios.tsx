"use client";

import { useState } from "react";
import { tableScenarios, TableScenario } from "../data/tableScenarios";
import ScenarioContent from "./ScenarioContent";

export default function TableScenarios() {
  const [selectedScenario, setSelectedScenario] = useState<string | null>(null);

  const getDifficultyBadge = (difficulty: string, color: string) => {
    const colorClasses = {
      green:
        "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
      yellow:
        "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
      red: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
    };

    return (
      <span
        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
          colorClasses[color as keyof typeof colorClasses]
        }`}
      >
        {difficulty === "Beginner" && "🟢"}
        {difficulty === "Intermediate" && "🟡"}
        {difficulty === "Advanced" && "🔴"} {difficulty}
      </span>
    );
  };

  if (selectedScenario) {
    const scenario = tableScenarios.find(
      (s: TableScenario) => s.id === selectedScenario
    );
    if (scenario) {
      return (
        <ScenarioContent
          scenario={scenario}
          onBack={() => setSelectedScenario(null)}
        />
      );
    }
  }

  return (
    <div className="space-y-4 sm:space-y-6">
      <div className="text-center space-y-4">
        <h2 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 dark:text-white">
          Choose Your Database Scenario
        </h2>
        <p className="text-sm sm:text-base text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
          Select a table scenario to learn database operations. Choose from
          basic CRUD-only scenarios or comprehensive examples that include
          normalization, relationships, and CRUD operations with both UI and
          Code methods.
        </p>
      </div>

      {/* Difficulty Level Legend */}
      <div className="bg-blue-50 dark:bg-gray-700 border border-blue-200 dark:border-blue-600 p-4 rounded-lg">
        <h3 className="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-3">
          Difficulty Levels
        </h3>
        <div className="flex flex-wrap gap-4 text-sm">
          <div className="flex items-center gap-2">
            <span className="text-green-600">🟢</span>
            <span className="text-blue-700 dark:text-blue-300">
              <strong>Beginner:</strong> CRUD-only or simple relationships
            </span>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-yellow-600">🟡</span>
            <span className="text-blue-700 dark:text-blue-300">
              <strong>Intermediate:</strong> Multiple tables, complex queries
            </span>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-red-600">🔴</span>
            <span className="text-blue-700 dark:text-blue-300">
              <strong>Advanced:</strong> Many-to-Many, complex normalization
            </span>
          </div>
        </div>
      </div>

      {/* Scenario Cards Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {tableScenarios.map((scenario: TableScenario) => (
          <div
            key={scenario.id}
            onClick={() => setSelectedScenario(scenario.id)}
            className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 cursor-pointer hover:shadow-lg hover:border-blue-300 dark:hover:border-blue-600 transition-all duration-200 transform hover:scale-105"
          >
            {/* Header with Icon and Difficulty */}
            <div className="flex items-start justify-between mb-4">
              <div className="text-3xl">{scenario.icon}</div>
              {getDifficultyBadge(
                scenario.difficulty,
                scenario.difficultyColor
              )}
            </div>

            {/* Title and Description */}
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              {scenario.title}
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
              {scenario.description}
            </p>

            {/* Tables */}
            <div className="mb-4">
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Tables:
              </h4>
              <div className="flex flex-wrap gap-1">
                {scenario.tables.map((table: string) => (
                  <span
                    key={table}
                    className="inline-block bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-2 py-1 rounded text-xs"
                  >
                    {table}
                  </span>
                ))}
              </div>
            </div>

            {/* Features */}
            <div>
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Features:
              </h4>
              <ul className="text-xs text-gray-600 dark:text-gray-400 space-y-1">
                {scenario.features.map((feature: string, index: number) => (
                  <li key={index} className="flex items-center gap-1">
                    <span className="text-green-500">✓</span>
                    {feature}
                  </li>
                ))}
              </ul>
            </div>

            {/* Click to Start */}
            <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
              <span className="text-sm font-medium text-blue-600 dark:text-blue-400">
                Click to start learning →
              </span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
