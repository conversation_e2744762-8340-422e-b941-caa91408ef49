/* Library Management System CSS Styles */
/* This file contains all the CSS styling for the library management CRUD operations */

/* Container and Layout */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    font-family: Arial, sans-serif;
}

/* Headers */
h1 {
    color: #2c3e50;
    text-align: center;
    margin-bottom: 30px;
    font-size: 2.5em;
}

h2 {
    color: #34495e;
    border-bottom: 2px solid #3498db;
    padding-bottom: 10px;
    margin: 30px 0 20px 0;
}

/* Navigation Buttons */
.nav-buttons {
    text-align: center;
    margin: 30px 0;
    padding: 20px;
    background-color: #ecf0f1;
    border-radius: 10px;
}

.btn {
    display: inline-block;
    padding: 12px 24px;
    margin: 5px 10px;
    background-color: #3498db;
    color: white;
    text-decoration: none;
    border-radius: 5px;
    border: none;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s;
}

.btn:hover {
    background-color: #2980b9;
}

/* Tables */
table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;
    background-color: white;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

th, td {
    padding: 12px;
    text-align: left;
    border: 1px solid #ddd;
}

th {
    background-color: #3498db;
    color: white;
    font-weight: bold;
}

tr:nth-child(even) {
    background-color: #f8f9fa;
}

tr:hover {
    background-color: #e8f4f8;
}

/* Forms */
.form-section {
    background-color: #f8f9fa;
    padding: 25px;
    margin: 20px 0;
    border-radius: 8px;
    border-left: 4px solid #3498db;
}

form {
    margin: 15px 0;
}

input[type="text"], 
input[type="email"], 
input[type="tel"],
input[type="date"],
input[type="number"],
select,
textarea {
    width: 100%;
    padding: 10px;
    margin: 8px 0;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-sizing: border-box;
    font-size: 14px;
}

input[type="submit"],
button {
    background-color: #27ae60;
    color: white;
    padding: 12px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    margin: 10px 5px;
}

input[type="submit"]:hover,
button:hover {
    background-color: #229954;
}

/* Status and Messages */
.status-available {
    color: #27ae60;
    font-weight: bold;
}

.status-borrowed {
    color: #e74c3c;
    font-weight: bold;
}

.success-message {
    background-color: #d4edda;
    color: #155724;
    padding: 15px;
    border: 1px solid #c3e6cb;
    border-radius: 4px;
    margin: 15px 0;
}

.error-message {
    background-color: #f8d7da;
    color: #721c24;
    padding: 15px;
    border: 1px solid #f5c6cb;
    border-radius: 4px;
    margin: 15px 0;
}

/* Statistics Cards */
.stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin: 30px 0;
}

.stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.stat-card h3 {
    margin: 0 0 10px 0;
    font-size: 2em;
}

.stat-card p {
    margin: 0;
    font-size: 1.1em;
}

/* Record Display */
.record {
    background-color: white;
    padding: 15px;
    margin: 10px 0;
    border-radius: 5px;
    border-left: 4px solid #3498db;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .nav-buttons .btn {
        display: block;
        margin: 10px 0;
    }
    
    table {
        font-size: 12px;
    }
    
    .stats {
        grid-template-columns: 1fr;
    }
}

/* Links */
a {
    color: #3498db;
    text-decoration: none;
}

a:hover {
    color: #2980b9;
    text-decoration: underline;
}
