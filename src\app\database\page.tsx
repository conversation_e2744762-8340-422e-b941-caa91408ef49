"use client";

import { useState } from "react";
import { tabs } from "./data/tabs";
import BasicCommands from "./components/BasicCommands";
import Setup from "./components/Setup";
import TableScenarios from "./components/TableScenarios";

export default function DatabasePage() {
  const [activeTab, setActiveTab] = useState("basics");

  return (
    <div className="max-w-7xl mx-auto px-2 sm:px-6 lg:px-8 py-4 sm:py-6 min-w-0">
      {/* Responsive Header */}
      <div className="text-center mb-6 sm:mb-8">
        <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white mb-2">
          Database Management Guide
        </h1>
        <p className="text-sm sm:text-base text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
          Simple step-by-step guide for database operations
        </p>
      </div>

      {/* Mobile-First Tab Navigation */}
      <div className="mb-6 sm:mb-8">
        {/* Mobile: Dropdown Style */}
        <div className="block sm:hidden">
          <select
            value={activeTab}
            onChange={(e) => setActiveTab(e.target.value)}
            className="w-full bg-gray-100 dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg px-4 py-3 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            {tabs.map((tab) => (
              <option key={tab.id} value={tab.id}>
                {tab.label}
              </option>
            ))}
          </select>
        </div>

        {/* Desktop: Tab Style */}
        <div className="hidden sm:flex justify-center">
          <div className="flex bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center px-4 lg:px-6 py-3 rounded-md transition-all text-sm lg:text-base whitespace-nowrap ${
                    activeTab === tab.id
                      ? "bg-blue-500 text-white"
                      : "text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
                  }`}
                >
                  <Icon className="h-4 w-4 mr-2" />
                  {tab.label}
                </button>
              );
            })}
          </div>
        </div>
      </div>

      {/* Mobile-Optimized Content Area */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-3 sm:p-6 lg:p-8 min-w-0">
        {activeTab === "basics" && <BasicCommands />}
        {activeTab === "setup" && <Setup />}
        {activeTab === "scenarios" && <TableScenarios />}
      </div>
    </div>
  );
}
