import Link from "next/link";
import {
  Database,
  Code,
  Network,
  Calculator,
  HardDrive,
  ArrowRight,
  BookOpen,
  Users,
  Award,
} from "lucide-react";

const modules = [
  {
    title: "Database Management & CRUD Operations",
    description:
      "Comprehensive student management system with normalized database design, CRUD operations, and data relationships.",
    href: "/database",
    icon: Database,
    features: [
      "Normalized 3NF Database",
      "CRUD Operations",
      "Data Relationships",
      "Authentication",
    ],
  },
  {
    title: "C Programming Showcase",
    description:
      "Interactive code examples demonstrating algorithms, patterns, and complexity analysis with syntax highlighting.",
    href: "/programming",
    icon: Code,
    features: [
      "Recursive Algorithms",
      "Star Patterns",
      "Complexity Analysis",
      "Interactive Examples",
    ],
  },
  {
    title: "Network Configuration Guide",
    description:
      "Comprehensive guide covering DNS hierarchy, VLAN configuration, and network diagrams with practical examples.",
    href: "/network",
    icon: Network,
    features: [
      "DNS Configuration",
      "VLAN Setup",
      "Network Diagrams",
      "Command Examples",
    ],
  },
  {
    title: "Interactive Subnetting Calculator",
    description:
      "Advanced subnet calculator with step-by-step calculations, binary representations, and VLSM support.",
    href: "/calculator",
    icon: Calculator,
    features: [
      "CIDR Notation",
      "Binary Display",
      "VLSM Support",
      "Step-by-step Process",
    ],
  },
  {
    title: "Hardware Component Gallery",
    description:
      "Comprehensive gallery of computer hardware components with specifications, comparisons, and interactive features.",
    href: "/hardware",
    icon: HardDrive,
    features: [
      "Component Categories",
      "Specifications",
      "Search & Filter",
      "Comparison Tools",
    ],
  },
];

const stats = [
  { label: "Modules", value: "5", icon: BookOpen },
  { label: "Components", value: "50+", icon: HardDrive },
  { label: "Code Examples", value: "20+", icon: Code },
  { label: "Interactive Features", value: "15+", icon: Users },
];

export default function Home() {
  return (
    <div className="space-y-16">
      {/* Hero Section */}
      <section className="text-center space-y-8">
        <div className="space-y-4">
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 dark:text-white">
            Practical Examination
            <span className="block text-blue-600 dark:text-blue-400">
              Application
            </span>
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            A comprehensive Next.js application showcasing five integrated
            modules: database management, programming concepts, network
            configuration, subnetting calculations, and hardware components.
          </p>
        </div>

        <div className="flex flex-wrap justify-center gap-4">
          <Link
            href="/database"
            className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Get Started
            <ArrowRight className="ml-2 h-4 w-4" />
          </Link>
          <Link
            href="#modules"
            className="inline-flex items-center px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
          >
            Explore Modules
          </Link>
        </div>
      </section>

      {/* Stats Section */}
      <section className="grid grid-cols-2 md:grid-cols-4 gap-8">
        {stats.map((stat) => {
          const Icon = stat.icon;
          return (
            <div key={stat.label} className="text-center">
              <div className="flex justify-center mb-2">
                <Icon className="h-8 w-8 text-blue-600 dark:text-blue-400" />
              </div>
              <div className="text-3xl font-bold text-gray-900 dark:text-white">
                {stat.value}
              </div>
              <div className="text-gray-600 dark:text-gray-300">
                {stat.label}
              </div>
            </div>
          );
        })}
      </section>

      {/* Modules Section */}
      <section id="modules" className="space-y-8">
        <div className="text-center">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            Application Modules
          </h2>
          <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            Each module demonstrates different aspects of modern web
            development, from database operations to interactive calculations.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {modules.map((module) => {
            const Icon = module.icon;
            return (
              <Link
                key={module.title}
                href={module.href}
                className="group block p-6 bg-white dark:bg-gray-800 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-200 dark:border-gray-700"
              >
                <div className="flex items-center mb-4">
                  <Icon className="h-8 w-8 text-blue-600 dark:text-blue-400 mr-3" />
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                    {module.title}
                  </h3>
                </div>

                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  {module.description}
                </p>

                <ul className="space-y-2">
                  {module.features.map((feature) => (
                    <li
                      key={feature}
                      className="flex items-center text-sm text-gray-500 dark:text-gray-400"
                    >
                      <Award className="h-4 w-4 text-green-500 mr-2" />
                      {feature}
                    </li>
                  ))}
                </ul>

                <div className="mt-4 flex items-center text-blue-600 dark:text-blue-400 group-hover:translate-x-1 transition-transform">
                  <span className="text-sm font-medium">Explore Module</span>
                  <ArrowRight className="ml-1 h-4 w-4" />
                </div>
              </Link>
            );
          })}
        </div>
      </section>

      {/* Features Section */}
      <section className="bg-gray-50 dark:bg-gray-800 rounded-2xl p-8">
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            Technical Features
          </h2>
          <p className="text-gray-600 dark:text-gray-300">
            Built with modern technologies and best practices
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="bg-blue-100 dark:bg-blue-900 rounded-lg p-4 mb-4 inline-block">
              <Code className="h-8 w-8 text-blue-600 dark:text-blue-400" />
            </div>
            <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
              Modern Stack
            </h3>
            <p className="text-gray-600 dark:text-gray-300 text-sm">
              Next.js 15, TypeScript, Tailwind CSS, Modern Components
            </p>
          </div>

          <div className="text-center">
            <div className="bg-green-100 dark:bg-green-900 rounded-lg p-4 mb-4 inline-block">
              <Database className="h-8 w-8 text-green-600 dark:text-green-400" />
            </div>
            <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
              Database Design
            </h3>
            <p className="text-gray-600 dark:text-gray-300 text-sm">
              Normalized schema, relationships, CRUD operations
            </p>
          </div>

          <div className="text-center">
            <div className="bg-purple-100 dark:bg-purple-900 rounded-lg p-4 mb-4 inline-block">
              <Users className="h-8 w-8 text-purple-600 dark:text-purple-400" />
            </div>
            <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
              User Experience
            </h3>
            <p className="text-gray-600 dark:text-gray-300 text-sm">
              Responsive design, dark mode, interactive components
            </p>
          </div>
        </div>
      </section>
    </div>
  );
}
