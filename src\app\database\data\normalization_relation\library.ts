// Library Management System Data
export const libraryData = {
  normalization: [
    {
      title: "📋  The Challenge - Try It Yourself!",
      description: `Here's an unnormalized library table. Can you figure out how to normalize it?
  
  **Original Messy Table:**
   BookID | Title | Author | ISBN | MemberID | MemberName | MemberEmail | Status 
  
  
  **Problems to Identify:**
  • What information is being repeated?
  • How many separate tables should this become?
  • What would be the primary keys for each table?
  
  **Your Challenge:** Think about how you would split this into clean, organized tables before looking at the solution!`,
      language: "text",
    },
    {
      title: "📋  Normalization Guide - XAMPP UI Method",
      description: `**Step-by-Step Guide to Create Normalized Tables in XAMPP**
  
  **Step 1: Open phpMyAdmin**
  1. Start XAMPP Control Panel
  2. Click "Start" for Apache and MySQL
  3. Click "Admin" next to MySQL to open phpMyAdmin
  4. Click "New" to create a new database
  5. Name it "library_management" and click "Create"
  
  **Step 2: Create Books Table**
  1. Click on your database name "library_management"
  2. Click "Create table"
  3. Table name: "tbl_books", Number of columns: 4
  4. Set up columns:
     - BookID: INT(10), Primary Key
     - Title: VARCHAR(255), NOT NULL
     - Author: VARCHAR(255), NOT NULL
     - ISBN: VARCHAR(20), UNIQUE
  
  **Step 3: Create Members Table**
  1. Click "Create table" again
  2. Table name: "tbl_members", Number of columns: 4
  3. Set up columns:
     - MemberID: INT, Primary Key, Auto Increment
     - MemberName: VARCHAR(255), NOT NULL
     - MemberEmail: VARCHAR(255), UNIQUE

  **Step 4: Create Borrowing Table**
  1. Click "Create table" again
  2. Table name: "tbl_borrowing", Number of columns: 6
  3. Set up columns:
     - BorrowID: INT, Primary Key, Auto Increment
     - BookID: INT, Foreign Key to tbl_books
     - MemberID: INT, Foreign Key to tbl_members
     - Status: ENUM('Borrowed','Returned','Overdue')
  
  **Step 5: Set Up Relationships**
  1. Go to tbl_borrowing table
  2. Click "Structure" tab
  3. Click "Relation View"
  4. Set BookID to reference tbl_books(BookID)
  5. Set MemberID to reference tbl_members(MemberID)
  
  ✅ **Done! Your normalized database is ready!**`,
      code: ``,
      language: "text",
    },
    {
      title: "Normalization Guide - SQL Code Method",
      description: `**Complete SQL Code to Create Normalized Library Database**
  
  Copy and paste this code into phpMyAdmin's SQL tab:`,
      code: `-- Step 1: Create Database
  CREATE DATABASE library_management;
  USE library_management;
  
  -- Step 2: Create Books Table
  CREATE TABLE tbl_books (
      BookID INT PRIMARY KEY AUTO_INCREMENT,
      Title VARCHAR(255) NOT NULL,
      Author VARCHAR(255) NOT NULL,
      ISBN VARCHAR(20) UNIQUE
  );

  -- Step 3: Create Members Table
  CREATE TABLE tbl_members (
      MemberID INT PRIMARY KEY AUTO_INCREMENT,
      MemberName VARCHAR(255) NOT NULL,
      MemberEmail VARCHAR(255) UNIQUE,
  );

  -- Step 4: Create Borrowing Table
  CREATE TABLE tbl_borrowing (
      BorrowID INT PRIMARY KEY AUTO_INCREMENT,
      BookID INT,
      MemberID INT,
      Status ENUM('Borrowed', 'Returned', 'Overdue') DEFAULT 'Borrowed',
      FOREIGN KEY (BookID) REFERENCES tbl_books(BookID),
      FOREIGN KEY (MemberID) REFERENCES tbl_members(MemberID)
  );
  
  -- Step 5: Insert Sample Data
  INSERT INTO tbl_books (Title, Author, ISBN) VALUES
  ('Database Systems', 'John Smith', '978-0123456789'),
  ('Web Development', 'Jane Doe', '978-0987654321'),
  ('Programming Logic', 'Mike Johnson', '978-0111222333');

  INSERT INTO tbl_members (MemberName, MemberEmail) VALUES
  ('Alice Johnson', '<EMAIL>'),
  ('Bob Wilson', '<EMAIL>),
  ('Carol Davis', '<EMAIL>');

  INSERT INTO tbl_borrowing (BookID, MemberID,  Status) VALUES
  (1, 1,  'Borrowed'),
  (2, 2,  'Returned'),
  (3, 1,  'Borrowed');`,
      language: "sql",
    },
  ],
  relationships: [
    {
      title: "Understanding Library Management Relationships",
      description: `**Why do we need relationships in our library management system?**

In a library, we have three main entities:
- **Books** (what can be borrowed)
- **Members** (who can borrow books)
- **Borrowing Records** (tracking who borrowed what and when)

**The Relationships:**
1. **Books → Borrowing**: One book can have multiple borrowing records (1-to-Many)
2. **Members → Borrowing**: One member can have multiple borrowing records (1-to-Many)

**Real-World Example:**
- "Database Systems" book can be borrowed by Alice in January, returned, then borrowed by Bob in February
- Alice can borrow "Database Systems" in January and "Web Development" in March
- Each borrowing record tracks exactly who borrowed which book and when

This way, we can easily track the complete borrowing history and current status of all books!`,
      code: ``,
      language: "text",
    },
  ],
};
